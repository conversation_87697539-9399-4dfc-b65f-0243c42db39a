//package com.paycraft.rta.abt.cam.cbt_integration.service.impl;
//
//import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_resources.ListType;
//import com.paycraft.rta.abt.cam.cbt_integration.mapper.UiListMapper;
//import com.paycraft.rta.abt.cam.cbt_integration.service.MobileResourceSoapService;
//import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.UiDataListRequestDTO;
//import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.UiDataListResponseDTO;
//import com.paycraft.rta.abt.cam.common.domain.entities.tibco.*;
//import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
//import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
//import com.paycraft.rta.abt.cam.common.repository.tibco.*;
//import jakarta.annotation.PostConstruct;
//import lombok.Getter;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.CompletableFuture;
//
//@Component
//public class StartUpService {
//    private static final Logger log = LogManager.getLogger(StartUpService.class);
//    @Autowired
//    private CamMstCbtArtWorkRepository camMstCbtArtWorkRepository;
//    @Autowired
//    private CamMstCbtConcessionTypeRepository camMstCbtConcessionTypeRepository;
//    @Autowired
//    private CamMstCbtCountryCodeRepository camMstCbtCountryCodeRepository;
//    @Autowired
//    private CamMstCbtCountryRepository camMstCbtCountryRepository;
//    @Autowired
//    private CamMstCbtLanguageRepository camMstCbtLanguageRepository;
//    @Autowired
//    private CamMstCbtReferenceDataRepository camMstCbtReferenceDataRepository;
//    @Autowired
//    private CamMstCbtTitleRepository camMstCbtTitleRepository;
//    @Autowired
//    private CamMstCbtServiceClassRepository camMstCbtServiceClassRepository;
//    @Autowired
//    private CamMstCbtNationalityRepository camMstCbtNationalityRepository;
//
//    @Autowired
//    private UiListMapper uiListMapper;
//
//    @Autowired
//    private MobileResourceSoapService mobileResourceSoapService;
//
//    private final Map<ListType, UiDataListRequestDTO> uiDataListRequestDTOMap = new HashMap<>();
//
//    @Getter
//    public static final Map<ListType, Object> uiDataListDetailsMap = new HashMap<>();
//
//    @PostConstruct
//    public void loadAtStartup() {
//        log.info("#######################   Initialize UiDataListRequestMap    ########################");
//        initializeUiDataListRequestDTOMap();
//        log.info("#######################   UiList at StartUp    ########################");
//        loadUiList();
//        log.info("#######################   load Ui-Data List Details Mapping    ########################");
//        loadUiDataListDetailsMap();
//    }
//
//    private void loadUiList() {
//        List<CompletableFuture<Void>> futures = Arrays.stream(ListType.values()).map(listType -> CompletableFuture.runAsync(() -> {
//            int x = listType.ordinal() + 1;
//            log.info("#####################   CBTSoapCalled for {} times", x);
//            UiDataListResponseDTO responseDTO = mobileResourceSoapService.getUIList(LanguageEnum.EN, uiDataListRequestDTOMap.get(listType));
//            log.info("####################  Response From Soap UI List : {}", responseDTO);
//            updateTableData(listType, responseDTO);
//        })).toList();
//
//        // Wait for all to complete before finishing startup
//        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
//    }
//
//    // Mapping requestDto for List of ListType.
//    private void initializeUiDataListRequestDTOMap() {
//        uiDataListRequestDTOMap.put(ListType.SPECIAL_LAYOUT, new UiDataListRequestDTO(YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.AREA_CODE, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.OFFICE_AREA_CODE, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.NATIONALITY, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.CONCESSION_TYPE, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.PASSENGER_CLASS, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.TITLE, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.EMIRATE, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.BUSINESS_ENTITY, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO));
//        uiDataListRequestDTOMap.put(ListType.COUNTRY_CODE, new UiDataListRequestDTO(YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.NO, YesOrNoEnum.YES));
//        // Add other ListType mappings similarly...
//    }
//
//    private void updateTableData(ListType listType, UiDataListResponseDTO responseDTO) {
//
//        switch (listType) {
//            case SPECIAL_LAYOUT:
//                List<CamMstCbtArtWork> camMstCbtArtWorkList = responseDTO.getArtWorks().stream()
//                        .map(response -> uiListMapper.mapToCamMstCbtArtWork(response))
//                        .toList();
//                camMstCbtArtWorkRepository.saveAllAndFlush(camMstCbtArtWorkList);
//                break;
//
//            // Add other cases as needed
//            case AREA_CODE:
//                List<CamMstCbtReferenceData> camMstCbtAreaCodes = responseDTO.getAreaCodes().stream()
//                        .map(response -> uiListMapper.mapAreaCodeToReferenceData(response)).toList();
//                camMstCbtReferenceDataRepository.saveAllAndFlush(camMstCbtAreaCodes);
//                break;
//
//            case OFFICE_AREA_CODE:
//                List<CamMstCbtReferenceData> camMstCbtOfficeAreaCodes = responseDTO.getOfficeAreaCodes().stream()
//                        .map(response -> uiListMapper.mapOfficeAreaCodeToReferenceData(response)).toList();
//                camMstCbtReferenceDataRepository.saveAllAndFlush(camMstCbtOfficeAreaCodes);
//                break;
//
//            // Add other cases as needed
//            case COUNTRY_CODE:
//                List<CamMstCbtCountryCode> camMstCbtCountryCodes = responseDTO.getCountryCodes().stream()
//                        .map(response -> uiListMapper.mapToCamMstCbtCountryCode(response)).toList();
//                camMstCbtCountryCodeRepository.saveAllAndFlush(camMstCbtCountryCodes);
//                break;
//
//            case CONCESSION_TYPE:
//                List<CamMstCbtConcessionType> camMstCbtConcessionTypes = responseDTO.getConcessionsTypes().stream()
//                        .map(response -> uiListMapper.mapToCamMstCbtConcessionType(response)).toList();
//                camMstCbtConcessionTypeRepository.saveAllAndFlush(camMstCbtConcessionTypes);
//                break;
//
//            case PASSENGER_CLASS:
//                List<CamMstCbtServiceClass> camMstCbtServiceClasses = responseDTO.getServiceClasses().stream()
//                        .map(response -> uiListMapper.mapToCamMstCbtServiceClass(response)).toList();
//                camMstCbtServiceClassRepository.saveAllAndFlush(camMstCbtServiceClasses);
//                break;
//
//            // Add other cases as needed
//            case TITLE:
//                List<CamMstCbtTitle> camMstCbtTitles = responseDTO.getServiceClasses().stream()
//                        .map(response -> uiListMapper.mapToCamMstCbtTitle(response)).toList();
//                camMstCbtTitleRepository.saveAllAndFlush(camMstCbtTitles);
//                break;
//
//            case EMIRATE:
//                List<CamMstCbtReferenceData> camMstCbtEmiratesData = responseDTO.getEmirates().stream()
//                        .map(response -> uiListMapper.mapEmiratesToReferenceData(response)).toList();
//                camMstCbtReferenceDataRepository.saveAllAndFlush(camMstCbtEmiratesData);
//                break;
//
//            case NATIONALITY:
//                List<CamMstCbtNationality> camMstCbtNationalities = responseDTO.getNationality().stream()
//                        .map(response -> uiListMapper.mapToCamMstCbtNationality(response)).toList();
//                camMstCbtNationalityRepository.saveAllAndFlush(camMstCbtNationalities);
//                break;
//            // Add other cases as needed
//            case BUSINESS_ENTITY:
//                // TODO : under discussion.
//                break;
//
//            default:
//                log.warn("Unhandled ListType in updateTableData: {}", listType);
//                break;
//        }
//    }
//
//
//    private void loadUiDataListDetailsMap() {
//        Arrays.stream(ListType.values()).forEach(type -> {
//            log.info("#######################   Ui Data List Details map for : {}    ########################",type);
//            switch (type) {
//                case SPECIAL_LAYOUT:
//                    List<CamMstCbtArtWork> artworks = camMstCbtArtWorkRepository.findAll();
//                    uiDataListDetailsMap.put(type, artworks);
//                    break;
//
//                case AREA_CODE:
//                    List<CamMstCbtReferenceData> areaCodes = camMstCbtReferenceDataRepository.findByRefKey(ListType.AREA_CODE.name());
//                    uiDataListDetailsMap.put(type, areaCodes);
//                    break;
//
//                case OFFICE_AREA_CODE:
//                    List<CamMstCbtReferenceData> officeAreaCodes = camMstCbtReferenceDataRepository.findByRefKey(ListType.OFFICE_AREA_CODE.name());
//                    uiDataListDetailsMap.put(type, officeAreaCodes);
//                    break;
//
//                case COUNTRY_CODE:
//                    List<CamMstCbtCountryCode> countryCodes = camMstCbtCountryCodeRepository.findAll();
//                    uiDataListDetailsMap.put(type, countryCodes);
//                    break;
//
//                case CONCESSION_TYPE:
//                    List<CamMstCbtConcessionType> concessions = camMstCbtConcessionTypeRepository.findAll();
//                    uiDataListDetailsMap.put(type, concessions);
//                    break;
//
//                case PASSENGER_CLASS:
//                    List<CamMstCbtServiceClass> serviceClasses = camMstCbtServiceClassRepository.findAll();
//                    uiDataListDetailsMap.put(type, serviceClasses);
//                    break;
//
//                case TITLE:
//                    List<CamMstCbtTitle> titles = camMstCbtTitleRepository.findAll();
//                    uiDataListDetailsMap.put(type, titles);
//                    break;
//
//                case EMIRATE:
//                    List<CamMstCbtReferenceData> emirates = camMstCbtReferenceDataRepository.findByRefKey(ListType.EMIRATE.name());
//                    uiDataListDetailsMap.put(type, emirates);
//                    break;
//
//                case NATIONALITY:
//                    List<CamMstCbtNationality> nationalities = camMstCbtNationalityRepository.findAll();
//                    uiDataListDetailsMap.put(type, nationalities);
//                    break;
//
//                case BUSINESS_ENTITY:
//                    // Skipped: under discussion or unimplemented
//                    log.warn("BUSINESS_ENTITY loading skipped (not yet implemented)");
//                    break;
//
//                default:
//                    log.warn("Unhandled ListType in loadUiDataListDetailsMap: {}", type);
//                    break;
//            }
//        });
//
//        log.info("#######################   UiDataListDetailsMap Initialized   ########################");
//    }
//}
