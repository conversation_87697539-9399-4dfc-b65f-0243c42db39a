package com.paycraft.rta.abt.cam.cbt_integration.domain.mapper;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.CardInfoAdvanceResponse;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.StudentConcessionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.CardInfoResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.CardTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.ConcessionTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.ServiceClassEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;

import javax.xml.datatype.XMLGregorianCalendar;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

public class CardInfoMapper {

    public static CardInfoResponseDTO toDTO(CardInfoAdvanceResponse source) {
        if (source == null) return null;
        CardInfoResponseDTO responseDTO = new CardInfoResponseDTO();
        responseDTO.setBalance(source.getBalance());
        responseDTO.setBalanceProtectionStatus(Objects.nonNull(source.isBalanceProtectionStatus()) && source.isBalanceProtectionStatus() ? YesOrNoEnum.YES:YesOrNoEnum.NO);
        if (source.getCardExpireDate() != null) {
            responseDTO.setCardExpiry(source.getCardExpireDate().toXMLFormat());
        }
        if (source.getCardStatus() != null) {
            responseDTO.setStatus(source.getCardStatus().value());
        }
        if (source.getCardType() != null) {
            responseDTO.setCardType(CardTypeEnum.CBT_TYPE_TO_CARD_TYPE_MAP.get(source.getCardType().value()));
        }
        if (source.getPendingAmount() != null) {
            responseDTO.setPendingAmount(source.getPendingAmount());
        }
        if (source.getConcessionType() != null) {
            responseDTO.setConcessionType(ConcessionTypeEnum.valueOf(source.getConcessionType().value()));
        }
        if (source.getMobileUserId() != null) {
            responseDTO.setMobileUserId(source.getMobileUserId());
        }
        if (source.getIsicNumber() != null) {
            responseDTO.setIsicSerialNumber(source.getIsicNumber());
        }
//        if (source.getIsicInstitutionName() != null) {
//            if (responseDTO.getStudentConcessionDetails() == null) {
//                responseDTO.setStudentConcessionDetails(new StudentConcessionDetailsDTO(null,null));
//            }
//            responseDTO.getStudentConcessionDetails().setSchoolShortName(source.getIsicInstitutionName());
//        }
//        if (source.getIsicHolderId() != null) {
//            if (responseDTO.getStudentConcessionDetails() == null) {
//                responseDTO.setStudentConcessionDetails(new StudentConcessionDetailsDTO());
//            }
//            responseDTO.getStudentConcessionDetails().setIsicCardHolderId(source.getIsicHolderId());
//        }
        if (source.getIsicHolderId() != null || source.getIsicInstitutionName() != null) {
            responseDTO.setStudentConcessionDetails(new StudentConcessionDetailsDTO(source.getIsicInstitutionName(), source.getIsicHolderId()));
        }

        if (source.getConcessionExpireDate() != null) {
            responseDTO.setConcessionExpiryDate(convertToLocalDateTime(source.getConcessionExpireDate()));
        }
        if (source.getMobileNumber() != null) {
            responseDTO.setMobileNumber(convertToMobileDTO(source.getMobileNumber()));
        }
        if (source.getPassengerClass() != null) {
            responseDTO.setServiceClass(ServiceClassEnum.valueOf(source.getPassengerClass().value()));
        }
        if (source.getEmail() != null) {
            responseDTO.setEmail(source.getEmail());
        }
        if (source.getIsPINValid() != null) {
            responseDTO.setIsPinValid(YesOrNoEnum.ID_TO_ENUM_MAP.get(source.getIsPINValid()));
        }
        if (source.getIsBlacklisted() != null) {
            //responseDTO.setIsBlackListed(YesOrNoEnum.valueOf(source.getIsBlacklisted()));
            responseDTO.setIsBlackListed(YesOrNoEnum.ID_TO_ENUM_MAP.get(source.getIsBlacklisted()));
        }
        return responseDTO;
    }
    private static MobileNumberDTO convertToMobileDTO(String number)
    {
        MobileNumberDTO numberDTO= new MobileNumberDTO();
        numberDTO.setCountryCode(number.substring(0,3));
        numberDTO.setAreaCode(String.valueOf(number.charAt(3)=='4'?4:number.substring(3,5)));
        numberDTO.setNumber(number.charAt(3)=='4'?number.substring(5):number.substring(6));
        return numberDTO;
    }
    private static LocalDateTime convertToLocalDateTime(XMLGregorianCalendar calendar) {
        if (calendar == null) {
            return null;
        }
        return calendar.toGregorianCalendar().toZonedDateTime().withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
    }
}
