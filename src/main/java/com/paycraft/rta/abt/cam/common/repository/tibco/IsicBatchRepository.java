package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.IsicSerialNumberDetail;
import feign.Param;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface IsicBatchRepository extends JpaRepository<IsicSerialNumberDetail, Long> {

    @Modifying
    @Transactional
    @Query(value = """
    UPDATE CAM_ISIC_SERIAL_NUMBER_DETAILS
    SET AUTO_VALIDATE_REF_ID = :autoRefId
    WHERE ID = (
        SELECT ID FROM (
            SELECT ID
            FROM CAM_ISIC_SERIAL_NUMBER_DETAILS
            WHERE STATUS = 'OPEN'
              AND AUTO_VALIDATE_REF_NO IS NULL
            ORDER BY ID
        ) WHERE ROWNUM = 1
    )
    """, nativeQuery = true)
    void updateFirstOpenAndNullAutoRefId(@Param("autoRefId") String autoRefId);

    IsicSerialNumberDetail findByAutoValidateRefNo(String autoValidationReferenceNumber);
}
