package com.paycraft.rta.abt.cam.common.domain.entities.weblogger;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Table(name="cam_web_request_interface")
@Getter
@Setter
public class WebRequestInterface {

    @Id
    @Column(name = "SERVICE_REQUEST_ID")
    private String srcReqId;

    @Column(name="SERVICE_NAME")
    private String srcName;

    @Column(name="REQUEST_URI")
    private String requestUri;

    @Column(name="request_body")
    private String requestBody;

    @Column(name="date_time")
    private Timestamp dateTime;

    @Column(name="request_start_time")
    private Long requestStartTime;

    @Column(name="api_type")
    private String apiType; // INTERNAL, EXTERNAL_SOAP, EXTERNAL_REST
}
