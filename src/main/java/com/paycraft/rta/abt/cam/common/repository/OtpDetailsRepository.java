package com.paycraft.rta.abt.cam.common.repository;

import com.paycraft.rta.abt.cam.common.domain.entities.CamOtpDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.Optional;

@Repository
public interface OtpDetailsRepository extends JpaRepository<CamOtpDetails, Long> {

    @Query("SELECT c.otp FROM CamOtpDetails c WHERE c.cardToken = :cardToken AND c.otpExpiryTime >= :transactionDateTime")
    String findOtpByCardToken(@Param("cardToken") String cardToken, @Param("transactionDateTime") Timestamp transactionDateTime);

    @Query("select (count(c) > 0) from CamOtpDetails c where c.transactionReferenceId = ?1 and c.status = ?2")
    boolean existsByTransactionReferenceIdAndStatus(String transactionReferenceId, String status);

    @Query("SELECT c FROM CamOtpDetails c WHERE c.cardToken = :cardToken AND c.transactionReferenceId = :transactionId")
    Optional<CamOtpDetails> findByCardToken(@Param("cardToken") String cardToken,@Param("transactionId") String txnId);

    @Modifying
    @Query("UPDATE CamOtpDetails o SET o.status = 'E' WHERE o.cardToken = :tokenId AND o.transactionReferenceId = :txnId")
    void expireOtp(@Param("tokenId") String tokenId, @Param("txnId") String txnId);


    @Query("SELECT c FROM CamOtpDetails c WHERE c.transactionReferenceId = :transactionReferenceId")
    Optional<CamOtpDetails> findByTransactionRefId(@Param("transactionReferenceId") String transactionReferenceId);

    @Query("SELECT c FROM CamOtpDetails c WHERE c.cardToken = :Id OR c.transactionReferenceId = :Id")
    Optional<CamOtpDetails> findByCardTokenOrTransactionRefId(@Param("Id") String id);
}
