package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class PcardConfirmRequestDTO extends RequestDTO {
    @NotNull
    private Map<String,String> paymentParameters;
    private PaymentMeansTypeEnum paymentMeansType;
    @NotBlank
    private String applicationReferenceNumber;
    @Schema(hidden = true)
    private String airwayBillNumber;
}
