package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.UserNameDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.AddressDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.UserDetailsRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.SalutationEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class RegisterCardValidateRequestDTO extends UserNameDetailsDTO {
    @Valid
    @NotNull
    private MobileNumberDTO mobileNumber;
    @Valid
    private MobileNumberDTO alternateMobileNumber;
    @NotBlank
    @Email
    private String email;
    @Valid
    @NotNull
    private AddressDTO address;
    private String documentGroupReferenceId;
    private PaymentMeansTypeEnum paymentMeansType;
    @NotNull
    private LocalDate dateOfBirth;
    private String mobileUserId;
    @NotBlank
    private String documentNumber;
    @NotBlank
    private String documentType;
    private Integer nationalityId;

}
