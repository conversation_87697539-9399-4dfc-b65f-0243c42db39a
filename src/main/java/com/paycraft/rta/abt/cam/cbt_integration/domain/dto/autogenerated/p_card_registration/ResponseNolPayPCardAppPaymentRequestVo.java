//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.ApplicationPaymentStatus;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for responseNolPayPCardAppPaymentRequestVo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="responseNolPayPCardAppPaymentRequestVo"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="referenceId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="paymentReference" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="appPaymentStatus" type="{http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema}applicationPaymentStatus" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "responseNolPayPCardAppPaymentRequestVo", propOrder = {
    "referenceId",
    "paymentReference",
    "appPaymentStatus"
})
public class ResponseNolPayPCardAppPaymentRequestVo {

    protected String referenceId;
    protected String paymentReference;
    @XmlSchemaType(name = "string")
protected ApplicationPaymentStatus appPaymentStatus;

    /**
     * Gets the value of the referenceId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReferenceId() {
        return referenceId;
    }

    /**
     * Sets the value of the referenceId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReferenceId(String value) {
        this.referenceId = value;
    }

    /**
     * Gets the value of the paymentReference property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPaymentReference() {
        return paymentReference;
    }

    /**
     * Sets the value of the paymentReference property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaymentReference(String value) {
        this.paymentReference = value;
    }

    /**
     * Gets the value of the appPaymentStatus property.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationPaymentStatus }
     *     
     */
public ApplicationPaymentStatus getAppPaymentStatus() {
        return appPaymentStatus;
    }

    /**
     * Sets the value of the appPaymentStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationPaymentStatus }
     *     
     */
    public void setAppPaymentStatus(ApplicationPaymentStatus value) {
        this.appPaymentStatus = value;
    }

}
