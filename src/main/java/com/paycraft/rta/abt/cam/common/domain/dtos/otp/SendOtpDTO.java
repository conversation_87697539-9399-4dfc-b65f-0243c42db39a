package com.paycraft.rta.abt.cam.common.domain.dtos.otp;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.SendOtpRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.CamOtpDetails;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SendOtpDTO {

    private String email;
    @NotNull
    private String otp;
    @Valid
    private MobileNumberDTO mobileNumber;
    @NotNull
    private String otpIdentifier;

    private LanguageEnum language;

    @NotBlank
    private String transactionId;

    public static SendOtpDTO from(CamOtpDetails otpDetails, SendOtpRequestDTO requestDTO) {
        SendOtpDTO dto = new SendOtpDTO();
        dto.setOtp(otpDetails.getOtp());
        dto.setEmail(requestDTO.getEmail());
        dto.setMobileNumber(requestDTO.getMobileNumber());
        dto.setOtpIdentifier(otpDetails.getTransactionReferenceId());
        dto.setTransactionId(requestDTO.getTransactionId());
        return dto;
    }
}