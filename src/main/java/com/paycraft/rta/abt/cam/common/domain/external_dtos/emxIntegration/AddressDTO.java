package com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AddressDTO {
    private String line1; // Mandatory
    private String line2;
    private String regionCode;
    private String regionName;
    private String city;  // Mandatory
    private String cityCode;
    private String state;
    private String countryCode; // Mandatory
    private String zipCode;
    private PointDTO point;
    private String category;
    private String streetName;
    private String landmark;
    private String buildingName;
    private String floorNo;
    private String apartmentNo;
    private String officeId;
    private String countryName;
    private String route;
    private String facility;
}
