//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for passengerClass.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <pre>
 * &lt;simpleType name="passengerClass"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="REGULAR"/&gt;
 *     &lt;enumeration value="GOLD"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "passengerClass")
@XmlEnum
public enum PassengerClass {

    REGULAR,
    GOLD;

    public String value() {
        return name();
    }

    public static PassengerClass fromValue(String v) {
        return valueOf(v);
    }

}
