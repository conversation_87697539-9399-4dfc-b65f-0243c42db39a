package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.file_upload.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.FileUploadService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CbtDocument;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.FileUploadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.FileUploadResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.CamDocumentDetails;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.repository.CamDocumentDetailsRepository;
import jakarta.transaction.Transactional;
import jakarta.xml.bind.JAXBElement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl implements FileUploadService {

    private final SoapClientRegistry soapClientRegistry;
    private final CamDocumentDetailsRepository camDocumentDetailsRepository;
    private static final String SERVICE_KEY = "fileUpload";
    private static final String SOAP_ENDPOINT = SoapUrlConstant.FILE_UPLOAD_SERVICE;
    private final ObjectFactory factory = new ObjectFactory();
    @Override
    @Transactional
    public FileUploadResponseDTO uploadFile(FileUploadRequestDTO requestDTO, LanguageEnum language) {
        List<CamDocumentDetails> camDocumentDetailsList = new ArrayList<>(); // ✅ fixed position

        try {
            if (requestDTO.getDocuments() == null || requestDTO.getDocuments().isEmpty()) {
                throw new RuntimeException("No documents found in the request.");
            }

            String documentGrpRefNo = camDocumentDetailsRepository.generateDocumentRefNo();
            for (CbtDocument doc : requestDTO.getDocuments()) {
                if (null == doc.getDocumentGroupReferenceId()) {
                    doc.setDocumentGroupReferenceId(documentGrpRefNo);
                }
                UploadFileRequest soapRequest = factory.createUploadFileRequest();
                if (doc.getDocumentFile() != null) soapRequest.setFileContentInBase64(doc.getDocumentFile());
                if (doc.getDocumentName() != null) soapRequest.setFileName(doc.getDocumentName());

                JAXBElement<UploadFileRequest> requestElement = factory.createUploadFileRequest(soapRequest);
                WebServiceTemplate webServiceTemplate = soapClientRegistry.getTemplate(SERVICE_KEY);

                JAXBElement<UploadFileResponse> soapResponse = (JAXBElement<UploadFileResponse>) webServiceTemplate.marshalSendAndReceive(
                        SOAP_ENDPOINT,
                        requestElement,
                        withHeader(language)
                );

                log.info("File upload response: {}", soapResponse.getValue());
                enrichDocumentDetails(requestDTO, doc, soapResponse.getValue(), camDocumentDetailsList);
            }

        } catch (SoapFaultClientException ex) {
            log.error("Inside File Upload SoapFaultClientException");
            throw parseSoapFaultException(ex);
        } catch (Exception e) {
            log.error("Inside File Upload Exception");
            throw new CbtIntegrationException(
                    null,
                    List.of(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), e.getMessage())),
                    MessageKeyEnum.SERVICE_CALL_FAILED.getCode(),
                    "File upload failed: " + e.getMessage()
            );
        }

        camDocumentDetailsRepository.saveAllAndFlush(camDocumentDetailsList);
        return new FileUploadResponseDTO(requestDTO.getDocuments().get(0).getDocumentGroupReferenceId());
    }

    private void enrichDocumentDetails(FileUploadRequestDTO requestDTO, CbtDocument doc, UploadFileResponse soapResponse, List<CamDocumentDetails> camDocumentDetailsList) {
        CamDocumentDetails camDocumentDetails = new CamDocumentDetails();

        if (doc.getDocumentGroupReferenceId() != null) camDocumentDetails.setDocumentGroupReferenceId(doc.getDocumentGroupReferenceId());
        if (doc.getDocumentId() != null) camDocumentDetails.setDocumentId(doc.getDocumentId());
        if (doc.getDocumentType() != null) camDocumentDetails.setDocumentType(doc.getDocumentType().getId());

        if (doc.getDocumentFile() != null) {
            byte[] fileBytes = Base64.getDecoder().decode(doc.getDocumentFile());
            camDocumentDetails.setDocumentFile(fileBytes);
            camDocumentDetails.setFileSize(fileBytes.length);
        }

        if (doc.getDocumentName() != null) camDocumentDetails.setDocumentName(doc.getDocumentName());
        if (soapResponse != null) {
            if (soapResponse.getFileCheckSum() != null) camDocumentDetails.setFileChecksum(soapResponse.getFileCheckSum());
            if (soapResponse.getFileReferenceNo() != null) camDocumentDetails.setFileReferenceNo(soapResponse.getFileReferenceNo());
            if (soapResponse.getFileSize() != null) camDocumentDetails.setFileSize(soapResponse.getFileSize().intValue());
        }

        if (requestDTO.getTransactionId() != null) camDocumentDetails.setTransactionId(requestDTO.getTransactionId());
        if (requestDTO.getRequestChannel() != null && requestDTO.getRequestChannel().getId() != null)
            camDocumentDetails.setRequestChannel(requestDTO.getRequestChannel().getId().toString());
        if (requestDTO.getIamUserId() != null) camDocumentDetails.setIamUserId(requestDTO.getIamUserId());
        if (requestDTO.getUaePassUserId() != null) camDocumentDetails.setUaePassUserId(requestDTO.getUaePassUserId());
        if (requestDTO.getAbtAccountId() != null) camDocumentDetails.setAbtAccountId(requestDTO.getAbtAccountId());

        if (requestDTO.getGuestUserInfo() != null) {
            if (requestDTO.getGuestUserInfo().getMobileNumber() != null) {
                if (requestDTO.getGuestUserInfo().getMobileNumber().getCountryCode() != null)
                    camDocumentDetails.setGuestCountryCode(requestDTO.getGuestUserInfo().getMobileNumber().getCountryCode());
                if (requestDTO.getGuestUserInfo().getMobileNumber().getAreaCode() != null)
                    camDocumentDetails.setGuestAreaCode(requestDTO.getGuestUserInfo().getMobileNumber().getAreaCode());
                if (requestDTO.getGuestUserInfo().getMobileNumber().getNumber() != null)
                    camDocumentDetails.setGuestNumber(requestDTO.getGuestUserInfo().getMobileNumber().getNumber());
            }
            if (requestDTO.getGuestUserInfo().getEmail() != null)
                camDocumentDetails.setGuestEmail(requestDTO.getGuestUserInfo().getEmail());
        }

        camDocumentDetailsList.add(camDocumentDetails);
    }

    private WebServiceMessageCallback withHeader(LanguageEnum language) {
        return message -> {
            try {
                SoapMessage soapMessage = (SoapMessage) message;
                soapMessage.setSoapAction("uploadFileInBase64");
                AcceptLanguages header = new AcceptLanguages();
                header.setLanguage(language != null ? language.name().toLowerCase() : "en");
                JAXBElement<AcceptLanguages> headerElement = new JAXBElement<>(
                        new QName("http://www.rta.ae/ActiveMatrix/ESB/AcceptLanguage/XMLSchema/", "AcceptLanguages"),
                        AcceptLanguages.class,
                        header
                );
                soapClientRegistry.getTemplate(SERVICE_KEY).getMarshaller().marshal(headerElement, soapMessage.getSoapHeader().getResult());
            } catch (Exception e) {
                throw new RuntimeException("Failed to add SOAP header", e);
            }
        };
    }

    private CbtIntegrationException parseSoapFaultException(SoapFaultClientException ex) {
        String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString())
                .map(cbtEnum -> cbtEnum.getErrorCode().toString())
                .orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());

        String faultMessage = ex.getFaultStringOrReason();
        List<ValidationResponseDTO> violations = new ArrayList<>();
        StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);

        String exceptionReferenceId = null;
        if (faultInfo != null) {
            exceptionReferenceId = faultInfo.getExceptionReferenceId();

            if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {
                for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                    String code = violation.getViolationCode();
                    String msg = violation.getMessage();
                    ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                    violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                }
            }

            if (faultInfo.getFieldValidations() != null) {
                for (FieldError fieldError : faultInfo.getFieldValidations()) {
                    violations.add(ValidationResponseDTO.of(
                            ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                            fieldError.getMessage() + ": " + fieldError.getField()));
                }
            }
        }

        return new CbtIntegrationException(exceptionReferenceId, violations, faultCode, faultMessage);
    }
}
