package com.paycraft.rta.abt.cam.cbt_integration.controller;

import com.paycraft.rta.abt.cam.cbt_integration.service.impl.MobileTravelPassServiceImpl;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.travelPass.*;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.*;

@RestController
public class TravelPassController {
    private static final Logger log = LogManager.getLogger(TravelPassController.class);
    @Autowired
    private TransactionAppResponseDTOAssembler assembler;
    @Autowired
     private MobileTravelPassServiceImpl travelPassSoapService;

    @PostMapping(TRAVEL_PASS_RENEW_VALIDATE)
    @Operation(summary = "Validate renew pass", description = "")
    public ResponseEntity<TransactionalAppResponseDTO> valdateFareProductRenewal(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody ValidateFareProductRenewalRequestDTO request) {
        log.info("Request for validate renew pass : {}",request.getTransactionId());
        travelPassSoapService.validateRenewRequest(clientLanguage,request);
        return ResponseEntity.ok(assembler.getResponse(null, MessageKeyEnum.SUCCESS, request.getTransactionId()));
    }
    @PostMapping(TRAVEL_PASS_RENEW_SUBMIT)
    @Operation(summary = "Submit renew pass", description = "")
    public ResponseEntity<TransactionalAppResponseDTO<SubmitFareProductRenewalResponseDTO>> submitFareProductRenewal(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody SubmitFareProductRenewalRequestDTO request) {
        log.info("Request for Submit renew pass :{}",request.getTransactionId());
        SubmitFareProductRenewalResponseDTO response  = travelPassSoapService.submitRenewRequest(clientLanguage,request);
        return ResponseEntity.ok(assembler.getResponse(response, MessageKeyEnum.SUCCESS, request.getTransactionId()));
    }
    @PostMapping(TRAVEL_PASS_RENEW_REVIEW)
    @Operation(summary = "Review renew pass", description = "")
    public ResponseEntity<TransactionalAppResponseDTO<ReviewFareProductRenewalResponseDTO>> renewFareProductRenewal(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody ReviewFareProductRenewalRequestDTO request) {
        log.info("Request for Review renew pass :{}",request.getTransactionId());
        ReviewFareProductRenewalResponseDTO response  = travelPassSoapService.reviewRenewRequest(clientLanguage,request);
        return ResponseEntity.ok(assembler.getResponse(response, MessageKeyEnum.SUCCESS, request.getTransactionId()));
    }

    @PostMapping(TRAVEL_PASS_RENEW_CONFIRM)
    @Operation(summary = "Confirm renew pass", description = "")
    public ResponseEntity<TransactionalAppResponseDTO<ConfirmFareProductRenewResponseDTO>> confirmFareProductRenew(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody ConfirmFareProductRenewRequestDTO request) {
        log.info("Request for Travel pass renew confirm :{}",request.getTransactionId());
        ConfirmFareProductRenewResponseDTO response  = travelPassSoapService.confirmRenewRequest(clientLanguage,request);
        return ResponseEntity.ok(assembler.getResponse(response, MessageKeyEnum.SUCCESS, request.getTransactionId()));
    }

    @PostMapping(TRAVEL_PASS_INFO)
    @Operation(summary = "Confirm renew pass", description = "")
    public ResponseEntity<TransactionalAppResponseDTO<TravelPassInfoResponseDTO>> getTravelPassInfo(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody TravelPassInfoRequestDTO request) {
        log.info("Request for Travel pass info :{} ",request.getTransactionId());
        TravelPassInfoResponseDTO response  = travelPassSoapService.getTravelPassInfo(clientLanguage,request);
        return ResponseEntity.ok(assembler.getResponse(response, MessageKeyEnum.SUCCESS, request.getTransactionId()));
    }
}


