package com.paycraft.rta.abt.cam.cbt_integration.domain.enums;
import lombok.AllArgsConstructor;
import lombok.Getter;


import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum CbtMessageKeyEnum {
    INVALID_CREDENTIALS("The provided authentication credentials are invalid", 3003,"1"),
    INVALID_REQUEST_FORMAT("Invalid Request Format", 3004, "3"),
    CHECK_SUM_FAILED("Check Sum Failed", 3005, "33"),
    SCHEMA_ERROR("Schema error", 3006, "6"),
    RTA_SERVICE_REFERENCE_DOES_NOT_EXIST("RTA Service Reference does not exist", 3007, "7"),
    TOP_UP_REQUEST_IS_NOT_IN_PENDING_STATUS("Top up request is not in pending status", 3008, "8"),
    TOP_UP_REQUEST_IS_EXPIRED("Top up request is expired", 3009, "9"),
    MPAY_CUSTOMER_ACCOUNT_IS_NOT_EXISTED("mPay Customer Account is not existed", 3010, "15"),
    NOL_TAG_ID_HAS_NOT_SUBSCRIBED("NOL Tag ID has not subscribed", 3011, "16"),
    BOTH_MOBILE_NUMBER_AND_TOP_UP_AMOUNT_CANNOT_BE_NULL("Both mobile number and top up amount cannot be null", 3012, "17"),
    INVALID_ADDRESS_EMIRATES("Invalid Address Emirates", 3013, "20"),
    NOL_CARD_IS_NOT_EXISTEDREGISTERED_IN_RTA_SYSTEM("NOL Card is not existed/registered in RTA System", 3014, "21"),
    NOL_CARD_IS_NOT_ACTIVATED("NOL Card is not activated", 3015, "22"),
    NOL_CARD_IS_BLOCKED("NOL Card is blocked", 3016, "23"),
    NOL_CARD_IS_EXPIRED("NOL Card is expired", 3017, "24"),
    NOL_CARD_IS_BLACKLISTED("NOL Card is blacklisted ", 3018, "25"),
    NOL_CARD_EPURSE_IS_BLOCKED("NOL Card ePurse is blocked", 3019, "26"),
    ALLOWABLE_TOP_UP_AMOUNT_IS_LESS_THAN_MINIMUM("Allowable top up amount is less than minimum", 3020, "27"),
    TOP_UP_AMOUNT_OUT_OF_BOUND("Top up amount out of bound", 3021, "28"),
    NOL_TAG_ID_ALREADY_SUBSCRIBED("NOL TAG ID already subscribed", 3022, "31"),
    INVALID_TRANSFER_TO_TAG_ID("Invalid Transfer to Tag ID", 3023, "41"),
    REFUND_CASE_DOES_NOT_EXIST("Refund Case does not exist", 3024, "42"),
    REFUND_ALREADY_COLLECTED("Refund already Collected", 3025, "43"),
    REFUND_IS_NOT_DONE_BY_CASH("Refund is not done by cash", 3026, "44"),
    INVALID_PIN("Invalid PIN", 3027, "45"),
    REFUND_IN_PROGRESS("Refund in progress", 3028, "46"),
    CARD_REPLACEMENT_IS_NOT_APPLICABLE_FOR_NON_PERSONALIZED_CARD("Card replacement is not applicable for non-Personalized Card", 3029, "47"),
    DUPLICATED_APPLICATION_REFERENCE_ID("Duplicated Application Reference ID", 3030, "48"),
    COMBI_CARD_APPROACH_BANK_TO_APPLY_REFUND("Combi card – approach Bank to apply refund", 3031, "49"),
    INVALID_CARD_TO_BE_REFUNDED("Invalid card to be refunded", 3032, "50"),
    PAYMENT_IS_NOT_SUCCESSFUL("Payment is not successful", 3033, "51"),
    PAYMENT_IS_NOT_YET_COMPLETED("Payment is not yet completed", 3034, "52"),
    PAYMENT_DOES_NOT_EXIST("Payment does not exist", 3035, "53"),
    DUPLICATED_REFUND_EXISTS("Duplicated Refund Exists", 3036, "54"),
    REFUND_REJECTED("Refund rejected", 3037, "55"),
    CARD_IS_EXPIRED_GOING_TO_BE_EXPIRED_WITHIN_IN_DAYS("Card is expired / going to be expired within {over Charge Approval Period} in Days", 3038, "58"),
    REQUESTED_REFUND_EXCEEDS_ELIGIBLE_OVERCHARGE_PERIOD("Requested refund exceeds eligible overcharge period", 3039, "59"),
    CARD_IS_NOT_VALID_AND_NOT_ELIGIBLE_TO_APPLY_FOR_REFUND("Card is not valid and not eligible to apply for refund", 3040, "60"),
    CASH_REFUND_AMOUNT_IS_GREATER_THAN_THE_MAXIMUM_ALLOWED_AMOUNT_PLEASE_PERFORM_BALANCE_TRANSFER_FOR_THE_REFUND("Cash Refund amount is greater than the maximum allowed amount, please perform balance transfer for the refund", 3041, "62"),
    EXPIRED_CARD_CANNOT_BE_REFUNDED("Expired card cannot be refunded", 3042, "63"),
    TRANSFER_REFUND_IS_NOT_APPLICABLE_FOR_THE_DESTINATION_CARD_ABOUT_TO_EXPIRE_EXPIRED_CARD("Transfer Refund is not applicable for the destination card about to expire / expired card", 3043, "64"),
    REFUND_CANNOT_BE_TRANSFERRED_TO_SAME_CARD_ID_PLEASE_UPDATE_AND_RETRY("Refund cannot be transferred to same Card ID, please update and retry.", 3044, "65"),
    REFUND_CANNOT_BE_TRANSFERRED_TO_AN_ANONYMIZED_CARD("Refund cannot be transferred to an Anonymous card", 3045, "66"),
    APPLICANT_AGE_SHOULD_BE_AT_LEAST_D_YEARS_OLD_FOR_STUDENT_CONCESSION("Applicant age should be at least %d years old for student concession.", 3046, "67"),
    APPLICANT_AGE_SHOULD_BE_BELOW_D_YEARS_OLD_FOR_STUDENT_CONCESSION("Applicant age should be below %d years old for student concession.", 3047, "68"),
    APPLICANT_AGE_SHOULD_BE_AT_LEAST_D_YEARS_OLD_FOR_SENIOR_CONCESSION("Applicant age should be at least %d years old for senior concession.", 3048, "69"),
    INTERNAL_SYSTEM_ERROR("Internal System Error", 3049, "99"),
    YOUR_REPLACEMENT_APPLICATION_IS_NOT_APPROVED("Your replacement application is not approved", 3050, "101"),
    REPLACEMENT_CARD_AMOUNT_NOT_FOUND_FOR_REFERENCE_ID("Replacement card amount not found for reference ID", 3051, "102"),
    INVALID_REFERENCE_ID("Invalid Reference ID", 3052, "103"),
    PAYMENT_REQUEST_FAILED_DUE_TO_YOUR_REPLACEMENT_APPLICATION_IS_REJECTED("Payment request failed due to your replacement application is rejected.", 3053, "104"),
    PAYMENT_REQUEST_ALREADY_INITIATED("Payment request already initiated.", 3054, "105"),
    YOUR_OLD_PCARD_APPLICATION_WAS_NOT_FOUND("Your old PCard application was not found.", 3055, "106"),
    YOUR_REPLACEMENT_APPLICATION_NOT_READY_FOR_PAYMENT("Your replacement application not ready for payment.", 3056, "107"),
    YOUR_REPLACEMENT_APPLICATION_IS_PAID("Your replacement application is paid.", 3057, "108"),
    ISIC_CARD_REPLACEMENT_SUBMISSION_IS_NOT_ALLOWED_IN_THIS_INTERFACE("ISIC Card replacement submission is not allowed in this interface.", 3058, "109"),
    INVALID_REQUEST_CHANNEL("Invalid Request Channel", 3059, "110"),
    YOUR_CARD_CONCESSION_IS_EXPIRED_OR_GOING_TO_EXPIRE_WITHIN_D_DAYS_PLEASE_CHOOSE_REASON_AS_RENEWAL_AND_SUBMIT("Your card concession is expired or going to expire within %d days. Please choose reason as Renewal and submit.", 3060, "111"),
    DUPLICATE_TRANSACTION_DETECTED("Duplicate Transaction Detected", 3061, null),
    DUPLICATE_TRANSACTION_ID("Duplicate Transaction Id", 3062, null),
    NO_PHOTO_FOUND_FOR_APPLICANT("No Photo found for the applicant", 3063, null);


    private final String message;
    private final Integer errorCode;
    private final String cbtErrorCode;

    public static final Map<String, CbtMessageKeyEnum> CBT_CODE_TO_ENUM_MAP =
            Stream.of(CbtMessageKeyEnum.values())
                    .filter(e -> e.getCbtErrorCode() != null)
                    .collect(Collectors.toMap(CbtMessageKeyEnum::getCbtErrorCode, e -> e));

    public static Optional<CbtMessageKeyEnum> fromCbtCode(String cbtCode) {
        return Optional.ofNullable(CBT_CODE_TO_ENUM_MAP.get(cbtCode));
    }

}
