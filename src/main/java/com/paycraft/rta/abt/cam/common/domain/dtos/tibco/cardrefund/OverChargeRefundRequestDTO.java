package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardrefund;

import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.MobileNumber;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OverChargeRefundRequestDTO extends CardRequestDTO {
    @Valid
    private MobileNumber mobileNumber;
    @Email
    private String email;
    @NotBlank
    private String refundReason;
    private String transferCardTagId;
    private LocalDateTime transactionDateTime;
    private String travelRoute;
}
