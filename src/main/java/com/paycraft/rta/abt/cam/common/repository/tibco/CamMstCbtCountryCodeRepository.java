package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtCountryCode;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtCountryCodeId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CamMstCbtCountryCodeRepository extends JpaRepository<CamMstCbtCountryCode, CamMstCbtCountryCodeId> {
    List<CamMstCbtCountryCode> findByLanguage(String languageCode);
}
