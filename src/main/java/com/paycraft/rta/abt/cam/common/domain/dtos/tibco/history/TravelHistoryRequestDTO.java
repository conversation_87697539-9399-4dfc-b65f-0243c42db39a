package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.HistoryTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TravelHistoryRequestDTO extends CardRequestDTO {
    private Integer noOfRecord;
//    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
//    private OffsetDateTime dateFrom;
//    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
//    private OffsetDateTime dateTo;
    private LocalDateTime dateFrom;
    private LocalDateTime dateTo;
    @Schema(hidden = true)
    private HistoryTypeEnum historyType;

}
