package com.paycraft.rta.abt.cam.common.domain.entities.weblogger;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Table(name="cam_web_response_interface")
@Getter
@Setter
public class WebResponseInterface {

    @Id
    @Column(name = "SERVICE_REQUEST_ID")
    private String srcReqId;

    @Column(name="REQUEST_URI")
    private String requestUri;

    @Column(name="response_code")
    private String responseCode;

    @Column(name="response_body")
    private String responseBody;

    @Column(name="error_message")
    private String errorMessage;

    @Column(name="date_time")
    private Timestamp dateTime;

    @Column(name="response_end_time")
    private Long responseEndTime;

    @Column(name="total_response_time_ms")
    private Long totalResponseTimeMs;

    @Column(name="external_api_time_ms")
    private Long externalApiTimeMs;

    @Column(name="internal_processing_time_ms")
    private Long internalProcessingTimeMs;
}
