package com.paycraft.rta.abt.cam.common.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Getter
@AllArgsConstructor
public enum ErrorCodesEnum {
    INVALID_CARD_TYPE("The card type specified in the request is not supported", 1000, "INVALID_CARD_TYPE"),
    CARD_NOT_FOUND("The specified card ID does not exist in the system", 1001, null),
    INTERNAL_ERROR("Internal Error occurred while processing your request. Kindly contact system administrator", 1002, null),
    INVALID_REQUEST_SOURCE("The request source specified is not valid", 1003, null),
    EXPIRED_CARD("The specified card has been expired", 1004, null),
    INVOKE_ERROR("An error occurred while invoking the Service: Default fault element. Kindly try again later", 1005, null),
    INVALID_TOP_UP_AMOUNT("The top-up amount specified is not one of the allowed values", 1006, null),
    CARD_BLACKLISTED("The specified card is blacklisted", 1007, "CARD_BLACKLISTED"),
    CARD_NOT_ENABLED("Card not enabled", 1008, "CARD_NOT_ENABLED"),
    EPURSE_BLACKLISTED("The associated E purse is blacklisted", 1009, "EPURSE_BLACKLISTED"),
    INVALID_PAYMENT_GATEWAY("Payment gateway is invalid", 1010, null),
    CRN_NOT_PRESENT("Missing Card Reference Number: CRN must be provided if present in the request", 1011, null),
    INVALID_TRANSACTION_CURRENCY("Missing or Invalid Transaction Currency: Currency must be valid (e.g., AED)", 1012, null),
    INVALID_TRANSACTION_AMOUNT("Missing or Invalid Transaction Amount: Transaction amount must be numeric", 1013, null),
    INVALID_TRANSACTION_TYPE("The transaction type is invalid", 1014, null),
    NOT_A_VALID_DATE_RANGE("The specified date range is not valid", 1015, "ILLEGAL_DATE_RANGE"),
    ILLEGAL_MAX_NUMBER_OF_RECORDS("The specified number of records is invalid", 1016, "ILLEGAL_MAX_NUM_RECORDS"),
    NOT_A_PERSONALIZED_CARD("The specified card is not a P card", 1017, "NOT_PERSONALIZED_CARD"),
    CARD_NO_PRODUCT("No travel pass is added on this nol card", 1018, "CARD_NO_PRODUCT"),
    CARD_PRODUCT_REFUND("Cannot renew travel pass as your travel pass has been already refunded", 1019, "CARD_PRODUCT_REFUND"),
    CARD_TRAVEL_PASS_NOT_ENABLED("Cannot renew travel pass as your travel pass is not enabled", 1020, null),
    PRODUCT_ID_NOT_FOUND("The specified product is invalid", 1021, null),
    PRODUCT_BLACKLISTED("Your travel pass is blacklisted and cannot be reloaded", 1022, "PRODUCT_BLACKLISTED"),
    RENEW_VALIDITY_EXPIRED("Cannot renew travel pass as renew duration did not begin yet", 1023, "RENEW_VALIDITY_EXPIRED"),
    RENEWED_PRODUCTS_EXIST("Your travel pass is already renewed", 1024, "RENEWED_PRODUCTS_EXIST"),
    INVALID_TRANSACTION_NUMBER("The specified transaction number is not valid", 1025, null),
    INPUT_DATA_INVALID("The input data is invalid", 1026, null),
    LOYALTY_ID_NOT_FOUND("No such loyalty id found", 1027, null),
    USER_ALREADY_EXIST("A user with the given identifier already exists", 1028, null),
    MISSING_FIELDS("One or more required fields are missing", 1029, null),
    INVALID_EMAIL_FORMAT("The email format is incorrect", 1030, null),
    INVALID_DOB("The date of birth is not in a valid format or not within an acceptable range", 1031, "INVALID_DATE_OF_BIRTH"),
    INVALID_MOBILE_NUMBER("The mobile number is not in a valid format", 1032, null),
    INVALID_CREDENTIALS("The provided authentication credentials are invalid", 1033, "1"),
    DATABASE_ERROR("A database error occurred during the user creation process", 1034, null),
    SERVICE_UNAVAILABLE("The server is not ready to handle the request due to maintenance or overloading", 1035, null),
    ACCOUNT_TYPE_ERROR("The account type is not a valid account type", 1036, null),
    UNAUTHORIZED_ACCESS("Access to fetch account/card information is unauthorized", 1037, null),
    TIMEOUT("The request timed out while fetching account information", 1038, null),
    OTP_UNAVAILABLE("OTP generation or retrieval is temporarily unavailable", 1039, null),
    TOO_MANY_REQUESTS("Too many requests", 1040, null),
    OTP_EXPIRED("The OTP entered has expired and cannot be validated", 1041, null),
    OTP_MISMATCH("The OTP entered does not match the expected value", 1042, null),
    SINGLE_ACCOUNT_NOT_FOUND("The single account specified for linking was not found", 1043, null),
    ALREADY_MASTER_ACCOUNT("The specified single account is already linked to a master account", 1044, null),
    MASTER_ACCOUNT_NOT_FOUND("The master account specified was not found", 1045, null),
    LINK_REQUEST_ALREADY_PROCESSED("The account link request has already been processed", 1046, null),
    ACCOUNT_LINK_REQUEST_NOT_FOUND("No account link request was found by specified individual account", 1047, null),
    NO_SINGLE_ACCOUNT_LINK_REQUEST_FOUND("The master account does not have any linked single accounts", 1048, null),
    SINGLE_LINK_ID_DOES_NOT_EXIST("The link ID provided does not exist", 1049, null),
    IAM_USER_ID_NOT_FOUND("The IAM user ID provided was not found", 1050, null),
    UAE_PASS_ID_NOT_FOUND("The UAE pass user ID provided was not found", 1051, null),
    ABT_USER_ID_NOT_FOUND("The ABT user ID provided was not found", 1052, null),
    EXPIRY_DATE_INVALID("The expiry date provided is invalid", 1053, null),
    CVV_CODE_INVALID("The CVV code provided is invalid", 1054, null),
    INVALID_BANK_CARD_ID("The bank card id provided is invalid", 1055, null),
    BANK_CARD_CANNOT_BE_DELETED("The bank card cannot be deleted as it is currently in use", 1056, null),
    NOL_CARD_VARIANTS_NOT_FOUND("Card variants not found", 1057, null),
    CONCESSION_TYPES_NOT_FOUND("Concession types not found", 1058, null),
    DOCUMENTS_INVALID_OR_MALFORMED("The request to upload documents is invalid or malformed", 1059, null),
    UNSUPPORTED_FILE_FORMAT("One or more documents have an unsupported file format", 1060, null),
    MAX_FILE_SIZE_LIMIT_EXCEEDED("Maximum file size limit exceeded for one or more documents", 1061, null),
    INVALID_NOL_CARD_VARIANT("Invalid Nol card variant selection", 1062, null),
    INVALID_SHIPPING_DETAILS("Missing or invalid shipping details", 1063, null),
    INSUFFICIENT_FUNDS("Insufficient funds", 1064, null),
    INVALID_TRACKING_ID("The tracking id is invalid", 1065, null),
    UNABLE_TO_RETRIEVE_DELIVERY_DETAILS("Unable to retrieve delivery details", 1066, null),
    PIN_FORMAT_INVALID("Invalid PIN format", 1067, null),
    AUTO_TOP_UP_DETAILS_NOT_FOUND("Auto top-up details not found for the given card", 1068, null),
    THRESHOLD_AMOUNTS_INVALID("Missing or invalid threshold amount", 1069, null),
    INVALID_SUSPENSION_DATE("Invalid or missing suspension start / end date", 1070, null),
    START_DATE_IS_AFTER_END_DATE("Start date is after end date", 1071, null),
    AUTO_RENEWAL_DETAILS_NOT_FOUND("Auto-renewal details not found", 1072, null),
    INVALID_AUTO_RENEWAL_ID("Invalid auto-renewal ID", 1073, null),
    UNSETTLED_DEBT_DETAILS_NOT_FOUND("Unsettled debt details not found", 1074, null),
    SOURCE_CARD_NOT_FOUND("Source card not found", 1075, null),
    DESTINATION_CARD_NOT_FOUND("Destination card not found", 1076, null),
    CARD_ALREADY_BLOCKED("Card already blocked", 1077, null),
    UNAUTHORIZED_BLOCK_REQUEST("Unauthorized block request", 1078, null),
    ERROR_RETRIEVING_BLOCKED_CARD_DETAILS("Error retrieving blocked card details", 1079, null),
    CARD_ALREADY_UNBLOCKED("Card already unblocked", 1080, null),
    REPLACEMENT_NOT_ALLOWED("Replacement for the card is not allowed based on current status or policy", 1081, null),
    ERROR_IN_CALCULATION("Error in calculating the replacement fee", 1082, null),
    REFUND_DENIED("Refund Denied: Refund request was denied", 1083, null),
    REFUND_REQUEST_ALREADY_EXISTS("Refund request already exists for this transaction", 1084, null),
    UNAUTHORIZED_REFUND_REQUEST("Unauthorized refund request", 1085, null),
    PRODUCT_NOT_ELIGIBLE("Product is not eligible for a refund", 1086, null),
    DUPLICATE_TRANSACTION("Duplicate transaction", 1087, null),
    IN_PROCESS("Transfer request already in process", 1088, null),
    INVALID_DEVICE_ID("Invalid device ID", 1089, null),
    INVALID_OPERATOR_ID("Invalid operator ID", 1090, null),
    INVALID_SHIFT_ID("Invalid shift ID", 1091, null),
    INVALID_TIER_ID("Invalid tier ID", 1092, null),
    INVALID_SERVICE_ID("Invalid service ID", 1093, null),
    MISSING_OR_INVALID_DEVICE_SERIAL_NUMBER("Missing or invalid device Serial Number", 1094, null),
    INVALID_NUMBER_OF_TICKETS("Invalid number of tickets", 1095, null),
    FARE_CALCULATION_FAILED("Fare calculation failed", 1096, null),
    TICKET_TIER_NOT_SUPPORTED("Ticket tier not supported", 1097, null),
    MISSING_OR_INVALID_MAC_ADDRESS("Missing or invalid MAC address", 1098, null),
    INVALID_ROUTE_ID("Invalid route Id", 1099, null),
    INVALID_LANGUAGE_CODE("Invalid language Code", 1100, null),
    INVALID_DEPARTURE_DATE("Invalid departure Date", 1101, null),
    INVALID_CORRELATION_ID("Invalid correlation Id", 1102, null),
    DOCUMENTS_REFERENCE_INVALID_OR_MALFORMED("Missing or invalid document reference details", 1103, null),
    PAYMENT_STATUS_NOT_SUCCESS("Payment is not successful", 1104, null),
    CANNOT_LINK_PERSONALIZED_CARD("Cannot Link Personalized/ Registered Card", 1105, null),
    CANNOT_SET_PIN_TO_ANONYMOUS_CARD("Cannot set pin to anonymous card", 1106, null),
    BULK_ORDER_GREATER_THAN_MAX("Bulk order greater than max", 1107, null),
    BULK_ORDER_LESS_THAN_MIN("Bulk order less than min", 1108, null),
    IAM_USER_ID_AND_UAE_PASS_ID_NOT_PROVIDED("Both Iam User Id and Uae pass id is not provided", 1109, null),
    IAM_USER_ID_AND_UAE_PASS_BOTH_PROVIDED("Both Iam User Id and Uae pass id is provided simultaneously.", 1110, null),
    IAM_USER_ID_AND_UAE_PASS_USER_ID_NOT_FOUND("The IAM user ID and UAE user ID provided was not found", 1111, null),
    ACCOUNT_ID_MISMATCH_FOR_USER_ID("The provided Account ID does not match the IAM User ID or UAE Pass ID.", 1112, null),
    CUSTOMER_ID_MISMATCH_FOR_USER_ID("The customer fetched using the IAM User ID does not match the customer fetched using the UAE Pass ID or vice versa.", 1113, null),
    ACCOUNT_ID_NOT_PROVIDED("ABT Account Id cant be null.", 1114, null),
    UAE_PASS_ID_NOT_EXIST_FOR_ACCOUNT("The UAE Pass ID does not exist for the account provided. UAE Pass ID must exist for upgradations.", 1115, null),
    EITHER_KYC_OR_DOCUMENT_DETAILS_ALLOWED("Either KYC Details or Document Details can be provided, but not both.", 1116, null),
    DOCUMENT_DETAILS_REQUIRES_IAM_USER("If Document Details are provided, IAM User ID must also be provided.", 1117, null),
    UAE_PASS_KYC_AND_IAM_REQUIRED("For UAE Pass, KYC details are mandatory along with IAM and UAE Pass ID.", 1118, null),
    ONLY_UAE_PASS_USER_CAN_CREATE_MASTER_ACCOUNT("Only UAE Pass users can create a Master Account. UAE Pass ID must be present", 1119, null),
    ACCOUNT_BLOCKED_FOR_LINK("account is blocked for linking", 1120, null),
    NOT_AN_ACTIVE_ACCOUNT("The specified account is not an active account", 1121, null),
    RESOURCE_NOT_FOUND("The requested resource was not found", 1122, null),
    PRODUCT_ALREADY_EXISTS("Product already exists", 1123, null),
    INFORMATION_ALREADY_EXISTS("Provided Information already exists", 1124, null),
    INVALID_REQUEST("Invalid request ", 1133, null),
    CMS_ERROR("{0}", 1134, null),
    INVALID_CARD_STATUS("Invalid card status", 1135, null),
    MOBILE_NOT_PRESENT("Mobile Number is not Available ", 1136, null),
    EMAIL_NOT_PRESENT("EmailId is not Available", 1137, null),
    NO_CONTACT_DETAILS("Mobile Number and EmailId both not Available ", 1138, null),
    INVALID_ACTIVATION_CODE("Activation code is Invalid/ Expired", 1139, null),
    INVALID_A_CARD_TYPE("Card must be Anonymous", 1140, null),
    ALREADY_REVERSAL_APPLIED("Already Reversal applied for the Transaction", 1141, null),
    STATEMENT_START_DATE_TOO_OLD("Invalid Statement Start Date: It should not be older than the allowed maximum history days", 1142, null),
    STATEMENT_END_DATE_BEFORE_START_DATE("Invalid Statement End Date: It should be after the Statement Start Date", 1143, null),
    EXPIRED_CARD_DATE("Card has expired. Please check the expiry date.", 1144, null),
    TOP_UP_AMOUNT_EXCEED_MAX_LIMIT("The top-up amount is more than of the allowed values.", 1145, null),
    TOP_UP_AMOUNT_LESS_THAN_MIN_LIMIT("The top-up amount is less than of the allowed values.", 1146, null),
    EXPIRED_PRODUCT("Product has expired. Please check the expiry date.", 1147, null),
    CARD_ALREADY_APPLIED("Application already filled for the provided Card Details", 1148, null),
    NOT_AN_REQUIRED_STATUS("Card status is not as per the required status ,", 1149, null),
    NOT_REQUIRED_CARD_TYPE("Card type is not as per the required type : ", 1150, null),
    RESOURCE_CONFLICT("Resource conflict. Request already exists in the AA (Awaiting Approval) state.", 1151, null),
    ACTION_ALREADY_PERFORMED("This Update action has already been performed for the given account number", 1152, null),
    INVALID_ACTION("The action type is not valid", 1153, null),
    INVALID_CARD_ISSUE_LIST("Card data is invalid from the list ", 1154, null),
    INVALID_PRELOAD_AMOUNT("Preload Amount Does Not Match", 1155, null),
    PRODUCT_NOT_FOUND("Product for provided code, Not found", 1156, null),
    AMOUNT_MISMATCHED_WRT_DATABASE("Amount(total) provided does not match the product price as in the Database ", 1157, null),
    INVALID_PAYMENT_AMOUNT("The Recieved amount does not match the sum of the received total and change.", 1158, null),
    INVALID_TRANSIT_CARD_STATUS("The card is not active. It may be blacklisted, expired, inactive, or permanently hot-listed.", 1159, null),
    UNSETTLED_DEBT("The card has unsettled debt.", 1160, null),
    INVALID_PRODUCT_START_DATE("The Product Start Date is invalid", 1161, null),
    INVALID_PRODUCT_FOR_CARD("Product is not eligible for the given card type", 1162, null),
    INVALID_REPLACEMENT_FEE("The replacement fee provided does not match the expected amount for the selected card.", 1163, null),
    LOYALTY_ALREADY_LINKED("Loyalty has already been linked for given card and loyalty id", 1164, null),
    LOYALTY_ALREADY_DELINKED("Loyalty has already been delinked for given card and loyalty id", 1165, null),
    VEHICLE_ALREADY_LINKED("Vehicle Number is already been linked for given card", 1166, null),
    VEHICLE_ALREADY_DELINKED("Vehicle Number is already been delinked for given card", 1167, null),
    INVALID_INPUT("Either NolTagId or ISIC Serial Number must be provided.", 1168, "INVALID_INPUT"),
    ACCOUNT_ALREADY_ACTIVE("The abt account is already active", 1169, null),
    ACCOUNT_ALREADY_INACTIVE("The abt account is already Inactive", 1170, null),
    ACTIVE_CARDS_FOR_ACCOUNT("The abt account has active cards", 1171, null),
    INVALID_OTP("Invalid OTP", 1172, null),
    CARD_ALREADY_DELINKED_TO_PORTAL("Card already de-linked to the Portal Login Id", 1173, null),
    CARD_ALREADY_LINKED_TO_PORTAL("Card already linked to the Portal Login Id", 1174, null),
    CARD_ALREADY_REGISTERED("Card already registered", 1197, null),
    NOT_NULL("Field cannot be Null", 1175, null),
    NOT_EMPTY("Field cannot be Empty", 1176, null),
    NOT_BLANK("Field cannot be Blank", 1177, null),
    SIZE("Field Exceeds maximum allowed Size", 1178, null),
    MIN("Field Exceeds threshold Size", 1179, null),
    MAX("Field Exceeds allowed maximum size", 1180, null),
    EMAIL("Email Id not valid", 1181, null),
    POSITIVE("Field must have only Positive value", 1182, null),
    POSITIVE_OR_ZERO("Field must have only Positive or Zero value", 1183, null),
    NEGATIVE("Field must have Negative value", 1184, null),
    NEGATIVE_OR_ZERO("Field must have Negative or Zero value", 1185, null),
    FUTURE("Field must be Future Dated", 1186, null),
    FUTURE_OR_PRESENT("Field must be Future or Present Dated", 1187, null),
    PAST("Field must have Past Date", 1188, null),
    PAST_OR_PRESENT("Field must have Past or Present Date", 1189, null),
    UniqueTransactionId("TransactionId must be Unique", 1190, null),
    DECIMAL_MIN("Decimal Exceeds threshold value", 1191, null),
    DECIMAL_MAX("Decimal Exceeds maximum value", 1192, null),
    DIGITS("Invalid Digits", 1193, null),
    CREDIT_CARD_NUMBER("Invalid Credit Card Number", 1194, null),
    LENGTH("Field length exceeds", 1195, null),
    PATTERN("Field has Invalid format ", 1196, null),
    ALREADY_MASTER("Only INDIVIDUAL accounts can be upgraded to MASTER.", 1198, null),
    CONCESSION_VERIFICATION_PENDING("Concession Verification Pending", 1199, null),
    INVALID_ENUM_VALUE("Invalid enum value ", 1200, null),
    PRODUCT_PRICE_MISMATCHED("Product Price provided does not match the price in database", 1201, null),
    AMOUNT_MISMATCHED_WRT_REQUEST("Amount(total) provided does not match the product price as in the Request ", 1202, null),
    ACCOUNT_ALREADY_EXISTS("ABT Account already exists for the request", 1203, null),
    E_WALLET_AUTO_RELOAD_ALREADY_EXISTS("E-wallet already has an auto-reload setting", 1204, null),
    AUTO_RELOAD_ALREADY_CANCELLED("Auto-reload setting is already cancelled", 1205, null),
    AUTO_RELOAD_ALREADY_ACTIVE("Auto-reload setting is already active", 1206, null),
    AUTO_RELOAD_ALREADY_SUSPENDED("Auto-reload setting is already suspended", 1207, null),
    AUTO_RELOAD_IS_CANCELLED("Cannot perform action; auto-reload setting is cancelled", 1208, null),
    INVALID_REQUEST_DETAILS("Invalid Details", 1209, null),
    NULL_POINTER_EXCEPTION("Null pointer exception", 1210, null),
    SERVICE_CALL_ERROR("Downstream system returned a business validation error", 1211, null),
    FAILED("OTP could not be sent to the intended recipient.", 1212, null),
    APPLICATION_NOT_FOUND("The application {Reference ID} does not exist.", 1213, "APPLICATION_NOT_FOUND"),
    APPLICATION_RECORD_MODIFIED_BY_OTHERS_EXECEPTION("Application record has been modified by others", 1214, "APPLICATION_RECORD_MODIFIED_BY_OTHERS_EXECEPTION"),
    GENERIC_EXCEPTION("(DSG Payment Service Exception) Create payment request web service with invalid response. DSG parameter detail {payment parameter detail}", 1215, "GENERIC_EXCEPTION"),
    APP_NOT_APPROVED("The application is not approved", 1216, "APP_NOT_APPROVED"),
    APP_REJECTED("The application is rejected", 1217, "APP_REJECTED"),
    REP_REJECTED("The replacement application is rejected", 1218, "REP_REJECTED"),
    REP_NOT_READY_FOR_PAYMENT("The application is not ready for payment", 1219, "REP_NOT_READY_FOR_PAYMENT"),
    OLD_CARD_REFUND_CASE_NOT_EXIST("The involved old card refund case is not exist.", 1220, "OLD_CARD_REFUND_CASE_NOT_EXIST"),
    OLD_CARD_APPICATION_RECORD_NOT_FOUND("The involved old card application record is not found.", 1221, "OLD_CARD_APPICATION_RECORD_NOT_FOUND"),
    APPICATION_CREATE_SETTLEMENT_TXN_EXCEPTION("Error when create settlement instruction for current application", 1222, "APPICATION_CREATE_SETTLEMENT_TXN_EXCEPTION"),
    OLD_CARD_REFUND_CASE_INVALID_PRODUCT_REMAIN_DAY_EXCEPTION("The involved old card refund cases product remain days is not valid", 1223, "OLD_CARD_REFUND_CASE_INVALID_PRODUCT_REMAIN_DAY_EXCEPTION"),
    APPLICATION_DOCUMENT_ID_APPIED_BEFORE_EXCEPTION("Applicant’s document ID has applied before", 1224, "APPLICATION_DOCUMENT_ID_APPIED_BEFORE_EXCEPTION"),
    APPLICATION_ALREADY_PROCESSING_EXCEPTION("Application already in processing", 1225, "APPLICATION_ALREADY_PROCESSING_EXCEPTION"),
    INVALID_APPLICATION("Invalid application reference ID", 1226, "INVALID_APPLICATION"),
    UNEXPECTED_SYSTEM_EXCEPTION("Unexpected System exception. error message ", 1227, "UNEXPECTED_SYSTEM_EXCEPTION"),
    INVALID_PAYMENT_CHANNEL("Mismatch Payment Initiation Channel", 1228, "INVALID_PAYMENT_CHANNEL"),
    INVALID_PAYMENT_REFERENCE("Invalid Payment Reference", 1229, "INVALID_PAYMENT_REFERENCE"),
    PAYMENT_COMPLETED("Your application is paid.", 1230, "PAYMENT_COMPLETED"),
    PAYMENT_NOT_INITIATED("Payment request has not been initiated", 1231, "PAYMENT_NOT_INITIATED"),
    PAYMENT_COMPELETED("The application payment has been confirmed", 1232, "PAYMENT_COMPELETED"),
    APPLICATION_ALREDY_HAVE_PAYEMENT_REQUEST("The payment request already initaled", 1233, "APPLICATION_ALREDY_HAVE_PAYEMENT_REQUEST"),
    AGE_VALIDATION_FAILED("Provided age does not meet the eligibility criteria for the selected concession type", 1234, "Provided age does not meet the eligibility criteria for the selected concession type."),
    INVALID_ISIC_SCHOOL_NAME("ISIC School name does not match", 1235, "ISIC School name does not match"),
    INVALID_SPONSOR_DETAILS("Invalid Sponsor details", 1236, "Invalid Sponsor Details"),
    OTP_ALREADY_VALIDATED("OTP already validated", 1237,null),
    INVALID_REQUEST_CHANNEL("Invalid request channel", 1238, "INVALID_REQUEST_CHANNEL"),
    CARD_NOT_EXIST("Card does not exist", 1239, "CARD_NOT_EXIST"),
    NOT_A_PCARD("Card is not a personalized card", 1240, "NOT_A_PCARD"),
    RENEWED_PRODUCT_EXIST("Renewed product already exists", 1241, "RENEWED_PRODUCT_EXIST"),
    TIER_NOT_FOUND("Tier not found", 1242, "TIER_NOT_FOUND"),
    VALIDITY_DURATION_NOT_FOUND("Validity duration not found", 1243, "VALIDITY_DURATION_NOT_FOUND"),
    CARD_PRODUCT_EXPIRED("Card product has expired", 1244, "CARD_PRODUCT_EXPIRED"),
    CARD_PRODUCT_UNABLE("Card product is not enabled", 1245, "CARD_PRODUCT_UNABLE"),
    NOT_LOGIN("User is not logged in", 1246, "NOT_LOGIN"),
    CARD_NOT_ELIGIBLE_TO_APPLY("Card is not eligible to apply", 1247, "CARD_NOT_ELIGIBLE_TO_APPLY"),
    DUPLICATED_APPLICATION("Duplicate application found", 1248, "DUPLICATED_APPLICATION"),
    CARD_ALREADY_PERSONALISED("Card already personalized", 1249, "CARD_ALREADY_PERSONALISED"),
    INVALID_TAGID("Invalid Tag ID", 1250, "INVALID_TAGID"),
    REFUND_TXN_EXCEPTION("Refund transaction for card with given card ID and cardGenNumber cannot be processed.", 1251, "REFUND_TXN_EXCEPTION"),
    BALANCE_PROTECTION_ALREADY_ENABLED("Balance protection already enabled", 1252, "BALANCE_PROTECTION_ALREADY_ENABLED"),
    INVALID_EMIRATE_DOCUMENT("Invalid emirate document", 1253, "INVALID_EMIRATE_DOCUMENT"),
    GUEST("Guest cards are not allowed for this operation", 1254, "GUEST"),
    NOT_PERSONALIZED("Card is not personalized", 1255, "NOT_PERSONALIZED"),
    NO_CARD_AGAINST_USER_PROFILE("No card found against the user profile", 1256, "NO_CARD_AGAINST_USER_PROFILE"),
    CARD_BLOCKED_OR_REFUNDED("Card is either blocked or refunded", 1257, "CARD_BLOCKED_OR_REFUNDED"),
    ILLEGAL_HISTORY_TYPE("Illegal history type", 1258, "ILLEGAL_HISTORY_TYPE"),
    CARD_KEY_NOT_FOUND("Given card ID and cardGenNumber not exists.", 1259, "CARD_KEY_NOT_FOUND"),
    LUHN_INVALID("Invalid Card ID.", 1260, "LUHN_INVALID"),
    TAG_ID_NOT_FOUND("Card with Tag ID does not exists.", 1261, "TAG_ID_NOT_FOUND"),
    CARD_ALREADY_EXIST_IN_ACCOUNT("Card with given tag id  already exists in your account.", 1262, "CARD_ALREADY_EXIST_IN_ACCOUNT"),
    CARD_REGISTERED_BY_OTHER_USER("Card with given tag id is already added by another user.", 1263, "CARD_REGISTERED_BY_OTHER_USER"),
    CARD_WITHOUT_PERSONALIZED("Only personalized card holders can access the service.  Please personalize your nol card.", 1264, "CARD_WITHOUT_PERSONALIZED"),
    UNMATCHED_PIN("Your PIN does not match", 1265, "UNMATCHED_PIN"),
    DUPLICATED_REQUEST_REFERENCE("Request identifier already exist", 1266, "DUPLICATED_REQUEST_REFERENCE"),
    DUPLICATED_SP_TXN_NUM("Service provider transaction number  already exist", 1267, "DUPLICATED_SP_TXN_NUM"),
    INVALID_IDOS_SERVICE_ID("IDOS Service ID is not valid.", 1268, "INVALID_IDOS_SERVICE_ID"),
    INVALID_PRODUCT_CODE("Product Code is not valid.", 1269, "INVALID_PRODUCT_CODE"),
    MISSING_REQUIRED_INFO("Required Info is missing.", 1270, "MISSING_REQUIRED_INFO"),
    APPLICATION_ALREADY_EXISTS("Application already exists.", 1271, "APPLICATION_ALREADY_EXISTS"),
    INVALID_APPROVAL_STATUS("The approval status of refund case is not valid.", 1272, "INVALID_APPROVAL_STATUS"),
    SERVICE_APPLICATION_EXCEPTION("Service Application exception ", 1273, "SERVICE_APPLICATION_EXCEPTION"),
    SYSTEM_EXCEPTION("System exception", 1274, "SYSTEM_EXCEPTION"),
    CARD_EXPIRED("Your nol card is expired and cannot be reloaded.", 1275, "CARD_EXPIRED"),
    INVALID_TOPUP_AMOUNT("Please select top up amount.", 1276, "INVALID_TOPUP_AMOUNT"),
    PRODUCT_EXPIRED("Cannot be renewed, as the travel pass is expired.", 1277, "PRODUCT_EXPIRED"),
    PAYMENT_CHANNEL_INVALID("Invalid Payment Channel.", 1278, "PAYMENT_CHANNEL_INVALID"),
    PAYMENT_CHANNEL_IS_NULL("Payment Channel cannot be null.", 1279, "PAYMENT_CHANNEL_IS_NULL"),
    AUTHENTICATION_FAIL("Authentication fail for payment transaction inquire.", 1280, "AUTHENTICATION_FAIL"),
    RECORD_NOT_FOUND("Payment transaction not found.", 1281, "RECORD_NOT_FOUND"),
    APPLICATION_ALREADY_HAVE_PAYEMENT_REQUEST("Your transaction payment is still pending, please check after sometime.", 1282, "APPLICATION_ALREADY_HAVE_PAYEMENT_REQUEST"),
    APPLICATION_PAYMENT_NOT_COMPLETE_EXCEPTION("Application payment is not complete", 1283, "APPLICATION_PAYMENT_NOT_COMPLETE_EXCEPTION"),
    INVALID_ADDRESS_EMIRATE("Invalid Address Emirates.", 1284, "INVALID_ADDRESS_EMIRATE"),
    INVALID_ADULT_CONTACT_INFORMATION("Contact information is invalid for Adult Card Type.", 1285, "INVALID_ADULT_CONTACT_INFORMATION"),
    INVALID_BE_ID("Please enter valid BE id.", 1286, "INVALID_BE_ID"),
    INVALID_CARD_MEDIA("Please enter valid card media.", 1287, "INVALID_CARD_MEDIA"),
    INVALID_CONCESSION_DOCUMENT("Invalid Concession Document", 1288, "INVALID_CONCESSION_DOCUMENT"),
    INVALID_CONCESSION_SENIOR("Your Date Of Birth does not match with concession type.", 1289, "INVALID_CONCESSION_SENIOR"),
    INVALID_DISABLED_CONTACT_INFORMATION("Contact information is invalid for Disabled Card Type.", 1290, "INVALID_DISABLED_CONTACT_INFORMATION"),
    INVALID_DOCUMENT_ID("Please enter valid document id.", 1291, "INVALID_DOCUMENT_ID"),
    INVALID_DOCUMENT_TYPE("Please enter valid Document Type.", 1292, "INVALID_DOCUMENT_TYPE"),
    FILE_ID_NOT_FOUND("Upload File with given file reference Id not exists.", 1293, "FILE_ID_NOT_FOUND"),
    INVALID_ISIC_CONCESSION("ISIC application only applicable to Student Concession", 1294, "INVALID_ISIC_CONCESSION"),
    INVALID_ISIC_INFORMATION("ISIC Information field is missing", 1295, "INVALID_ISIC_INFORMATION"),
    INVALID_MARITAL_STATUS("Please enter valid Marital Status.", 1296, "INVALID_MARITAL_STATUS"),
    INVALID_MAX_CONCESSION_STUDENT_AGE(null, 1297, "INVALID_MAX_CONCESSION_STUDENT_AGE"),
    INVALID_MIN_CONCESSION_SENIOR_AGE(null, 1298, "INVALID_MIN_CONCESSION_SENIOR_AGE"),
    INVALID_MIN_CONCESSION_STUDENT_AGE(null, 1299, "INVALID_MIN_CONCESSION_STUDENT_AGE"),
    INVALID_NATIONALITY("Please enter valid Nationality.", 1300, "INVALID_NATIONALITY"),
    INVALID_PENSIONER_CONTACT_INFORMATION("Contact information is invalid for Senior Citizen Card Type.", 1301, "INVALID_PENSIONER_CONTACT_INFORMATION"),
    INVALID_PHOTO("Invalid Photo", 1302, "INVALID_PHOTO"),
    INVALID_SEX("Please enter valid Gender", 1303, "INVALID_SEX"),
    INVALID_STUDENT_CONTACT_INFORMATION("Contact information is invalid for Student Card Type.", 1304, "INVALID_STUDENT_CONTACT_INFORMATION"),
    PAYMENT_REQ_EXISTS("Payment request already initiated.", 1305, "PAYMENT_REQ_EXISTS"),
    PCARD_APPLICATION_DUPLICATED("There is an application associated with the Emirates ID already. Please provide another Emirates ID.", 1306, "PCARD_APPLICATION_DUPLICATED"),
    APP_NOT_ALLOWED("Application does not allowed.", 1307, "APP_NOT_ALLOWED"),
    CONCESSION_DOC_REQUIRED("Concession Document Required.", 1308, "CONCESSION_DOC_REQUIRED"),
    GUARDIAN_DOC_REQUIRED("Guardian Document Required.", 1309, "GUARDIAN_DOC_REQUIRED"),
    INVALID_REFUND_REG_NUMBER("Please input a valid Reference ID.", 1310, "INVALID_REFUND_REG_NUMBER"),
    COMBI_CARD_NOT_SUPPORT("Combi Card is not supported.", 1311, "COMBI_CARD_NOT_SUPPORT"),
    INVALID_PIN("Invalid Pin.", 1312, "INVALID_PIN"),
    TAG_ID_NOT_EXIST("Invalid Tag ID.", 1313, "TAG_ID_NOT_EXIST"),
    USER_NOT_MATCH("The card is not owned by the user.", 1314, "USER_NOT_MATCH"),
    BLACKLIST_EXISTS("Your nol card is blacklisted.", 1315, "BLACKLIST_EXISTS"),
    BLACKLIST_GENERAL_EXCEPTION("Unable to create blacklist.", 1316, "BLACKLIST_GENERAL_EXCEPTION"),
    BLACKLIST_INVALID_INPUT("Invalid input for blacklist with effective date  and expire date ", 1317, "BLACKLIST_INVALID_INPUT"),
    CARD_ID_NOT_FOUND("The card does not exist.", 1318, "CARD_ID_NOT_FOUND"),
    INCOMPLETE_CUSTOMER_DATA("Unable to create refund case of card with given card ID and cardGenNumber with incomplete customer information.", 1319, "INCOMPLETE_CUSTOMER_DATA"),
    REFUND_CASE_EXISTS("Your nol card is refunded.", 1320, "REFUND_CASE_EXISTS"),
    REFUND_INVALID_INPUT("Invalid Input.", 1321, "REFUND_INVALID_INPUT"),
    CARD_HAS_WHITELIST("Card is whitelisted and not eligible for this operation", 1322, "CARD_HAS_WHITELIST"),
    MOBILE_NUMBER_NOT_VALIDATED("Mobile Number not validated",1323,"Mobile Number not validated"),
    EMAIL_NOT_VALIDATED("Email not validated",1324,"Email not validated"),
    CHECKS_NOT_MATCHED("Checks in request not matched in database" , 1325 , "Checks in request not matched in databse" ),
    OTP_NOT_VALIDATED("Otp not in valid state",1326,"Otp not in valid state");



    private final String message;
    private final Integer errorCode;
    private final String cbtErrorCode;

    public static final Map<Integer, ErrorCodesEnum> ERROR_CODE_TO_ENUM_MAP =
            Stream.of(ErrorCodesEnum.values())
                    .collect(Collectors.toMap(ErrorCodesEnum::getErrorCode, errorCodeEnum -> errorCodeEnum));

    public static final Map<String, ErrorCodesEnum> CBT_CODE_TO_ENUM_MAP =
            Stream.of(ErrorCodesEnum.values())
                    .filter(e -> e.getCbtErrorCode() != null)
                    .collect(Collectors.toMap(ErrorCodesEnum::getCbtErrorCode, e -> e));

    public static Optional<ErrorCodesEnum> fromCbtCode(String cbtCode) {
        return Optional.ofNullable(CBT_CODE_TO_ENUM_MAP.get(cbtCode));
    }


}
