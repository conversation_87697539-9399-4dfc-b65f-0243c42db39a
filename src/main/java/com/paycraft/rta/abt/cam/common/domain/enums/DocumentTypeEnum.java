package com.paycraft.rta.abt.cam.common.domain.enums;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.ReferenceDetails;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum DocumentTypeEnum {
    PASSPORT(Short.valueOf("1"), "Passport", "Passport", "جواز السفر","P"),
    EMIRATES_ID(Short.valueOf("2"), "Emirates ID", "Emirates ID", "الهوية الإماراتية","N"),
    STUDENT_SUPPORTING_DOCUMENT(Short.valueOf("3"), "Student supporting document", "Student Document", "مستند الطالب",null),
    POD_SUPPORTING_DOCUMENT(Short.valueOf("4"), "POD supporting document", "POD Document", "مستند إثبات العنوان",null),
    SPONSOR_SUPPORTING_DOCUMENT(Short.valueOf("5"), "Sponsor supporting document", "Sponsor Document", "مستند الكفيل",null),
    PHOTO(Short.valueOf("6"), "Sponsor supporting document", "Sponsor Document", "مستند الكفيل",null);

    private final Short id;
    private final String description;
    private final String nameEn;
    private final String nameAr;
    private final String cbtType;
    public static final Map<Short, DocumentTypeEnum> ID_TO_DOCUMENT_TYPE_MAP =
            Stream.of(DocumentTypeEnum.values())
                    .collect(Collectors.toMap(DocumentTypeEnum::getId, documentTypeEnum -> documentTypeEnum));


    public static List<ReferenceDetails> getReferenceDetails() {
        return Stream.of(DocumentTypeEnum.values())
                .filter(documentTypeEnum -> Objects.nonNull(documentTypeEnum.cbtType))
                .map(type -> new ReferenceDetails(
                        type.name(),         // ID from enum name
                        type.getNameEn(),    // English name
                        type.getNameAr()     // Arabic name
                ))
                .collect(Collectors.toList());
    }
}
