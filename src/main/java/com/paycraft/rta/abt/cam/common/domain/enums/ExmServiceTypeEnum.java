package com.paycraft.rta.abt.cam.common.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ExmServiceTypeEnum {
    // Mandatory (Domestic,International,EMS, None,SameDay,PrimeRegistered,PrimeTracked,PrimeExpres,Registered,Parcel,EconomyParcel,Return, Standard) TODO : Enum
    DOMESTIC("Domestic"),
    INTERNATIONAL("International"),
    EMS("EMS"),
    NONE("None"),
    SAME_DAY("SameDay"),
    PRIME_REGISTERED("PrimeRegistered"),
    PRIME_TRACKED("PrimeTracked"),
    PRIME_EXPRESS("PrimeExpres"), REGISTERED_PARCEL("Registered"),PARCEL("Parcel"), ECONOMY_PARCEL("EconomyParcel"), RETURN("Return"), STANDARD("Standard");

    private final String cbtType;
}
