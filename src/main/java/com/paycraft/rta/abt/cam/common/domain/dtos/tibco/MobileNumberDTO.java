package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class MobileNumberDTO {
    @NotBlank
    @Size(max=3)
    private String countryCode;
    @NotBlank
    @Size(max=2)
    private String areaCode;
    @NotBlank
    @Size(max=7)
    private String number;
}
