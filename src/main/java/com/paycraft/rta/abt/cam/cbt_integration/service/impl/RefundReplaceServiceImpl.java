package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.refund_admin_external.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.RefundReplaceService;

import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;

import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.MobileNumber;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardPaymentParamsRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardPaymentParamsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.ConfirmCardReplacementRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.ConfirmCardReplacementResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.SubmitCardReplacementRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.SubmitCardReplacementResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardrefund.CardRefundRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardrefund.CardRefundResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardrefund.OverChargeRefundRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardrefund.OverChargeRefundResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import com.paycraft.rta.abt.cam.common.domain.enums.CardActionEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.ADMIN_REFUND_REPLACE;

@Service
public class RefundReplaceServiceImpl implements RefundReplaceService {

    private static final Logger log = LogManager.getLogger(RefundReplaceServiceImpl.class);
    private static final String SERVICE_KEY = "refundAdminExternal";

    @Autowired
    private TransactionAppResponseDTOAssembler assembler;


    //private final WebServiceTemplate template;
    private final SoapClientRegistry soapClientRegistry;
    private final ObjectFactory factory = new ObjectFactory();

    public RefundReplaceServiceImpl(SoapClientRegistry soapClientRegistry) {
        this.soapClientRegistry = soapClientRegistry;
        //this.template = soapClientRegistry.getTemplate(SERVICE_KEY);
    }

    @Override
    public TransactionalAppResponseDTO<OverChargeRefundResponseDTO> overChargeRefund(LanguageEnum language, OverChargeRefundRequestDTO requestDTO) {
        try {
            OverChargeRefundCreationRequest request = new OverChargeRefundCreationRequest();
            request.setChannel(requestDTO.getRequestChannel().getCbtType());
            request.setCardTagId(Long.valueOf(requestDTO.getNolCardId()));
            MobileNumber mobile = requestDTO.getMobileNumber();//keep it MobileNumber NOT MobileNumberDTO it is req for validations as per doc
            String fullMobileNumber = mobile.getCountryCode().replace("+", "") + mobile.getAreaCode() + mobile.getNumber();
            request.setMobileNumber(fullMobileNumber);
            request.setEmail(requestDTO.getEmail());
            request.setRefundReason(requestDTO.getRefundReason());
            request.setUsageTime(toXmlGregorianCalendar(requestDTO.getTransactionDateTime()));
            request.setTransferCardTagId(Long.valueOf(requestDTO.getTransferCardTagId()));
            request.setTravelRoute(requestDTO.getTravelRoute());
            request.setBusinessEntityId(Long.valueOf(requestDTO.getBeId()));

            JAXBElement<OverChargeRefundCreationRequest> jaxbRequest = factory.createOverChargeRefundCreation(request);
            JAXBElement<OverChargeRefundCreationResponse> soapResponse = (JAXBElement<OverChargeRefundCreationResponse>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(ADMIN_REFUND_REPLACE, jaxbRequest, withSoapAction("overChargeRefundCreation"));
            log.info("Received SOAP response for overChargeRefund: {}", soapResponse.getValue());

            if (!Long.valueOf(0).equals(soapResponse.getValue().getResponseCode())) {
                throw new CbtIntegrationException(null, null, getErrorCode(soapResponse.getValue().getResponseCode()), soapResponse.getValue().getResponseDescription());
            }
            OverChargeRefundResponseDTO response = new OverChargeRefundResponseDTO();
            response.setRefundReferenceId(soapResponse.getValue().getRefundReferenceId());
            response.setRtaReferenceNumber(soapResponse.getValue().getRtaReferenceNumber());
            log.info("overChargeRefund successful for NolCardId: {}", requestDTO.getNolCardId());
            return assembler.getResponse(response, MessageKeyEnum.SUCCESS, null);

        } catch (SoapFaultClientException ex) {
            log.error("SOAP Fault during overChargeRefund for NolCardId: {}", requestDTO.getNolCardId(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString())
                    .map(code -> code.getErrorCode().toString())
                    .orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            String exceptionRefId = null;
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            if (faultInfo != null) {
                exceptionRefId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {
                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum error = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(error.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exceptionRefId, violations, faultCode, faultMessage);
        }
    }

    private XMLGregorianCalendar toXmlGregorianCalendar(LocalDateTime localDateTime) {
        if (localDateTime == null) return null;
        try {
            ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
            GregorianCalendar gregorianCalendar = GregorianCalendar.from(zdt);
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
        } catch (DatatypeConfigurationException e) {
            throw new RuntimeException("Failed to convert LocalDateTime to XMLGregorianCalendar", e);
        }
    }

    @Override
    public TransactionalAppResponseDTO<CardRefundResponseDTO> confirmCardRefund(LanguageEnum language, CardRefundRequestDTO requestDTO) {
        try {
            CardRefundCreationRequest request = new CardRefundCreationRequest();
            request.setCardTagId(Long.valueOf(requestDTO.getNolCardId()));
            MobileNumberDTO mobile = requestDTO.getMobileNumber();
            if (mobile != null && StringUtils.isNoneEmpty(mobile.getAreaCode(), mobile.getCountryCode(), mobile.getNumber())) {
                String fullMobileNumber = mobile.getCountryCode().replace("+", "") + mobile.getAreaCode() + mobile.getNumber();
                request.setMobileNumber(fullMobileNumber);
            }
            MobileNumberDTO alternetmobile = requestDTO.getAlternateMobileNumber();
            if (mobile != null && StringUtils.isNoneEmpty(alternetmobile.getAreaCode(), alternetmobile.getCountryCode(), alternetmobile.getNumber())) {
                String fullAlternetMobileNumber = alternetmobile.getCountryCode().replace("+", "") + alternetmobile.getAreaCode() + alternetmobile.getNumber();
                request.setAlternateMobileNumber(fullAlternetMobileNumber);
            }

            request.setEmail(requestDTO.getEmail());
            request.setPin(requestDTO.getPin());
            request.setReason(requestDTO.getReason().getCbtType());
            request.setIsCardRetained(Objects.nonNull(requestDTO.getIsCardRetained()) ? requestDTO.getIsCardRetained().getCbtType() : YesOrNoEnum.NO.getCbtType());
            request.setRefundMethod(requestDTO.getRefundMode().getId());
            if (requestDTO.getTransferNolCardId() != null) {
                request.setTransferCardTagId(Long.valueOf(requestDTO.getTransferNolCardId()));
            }
            if (requestDTO.getLocationId() != null) {
                request.setLocationId(Long.valueOf(requestDTO.getLocationId()));
            }
            if (requestDTO.getBeId() != null) {
                request.setBusinessEntityId(Long.valueOf(requestDTO.getBeId()));
            }
            if (requestDTO.getIsIsicEnabled() != null) {
                request.setIsIsicEnabled(requestDTO.getIsIsicEnabled().getCbtType());
            }
            if (requestDTO.getIsIsicEnabled() != null) {
                request.setIsicNumber(requestDTO.getIsicSerialNumber());
            }
            if (requestDTO.getSchoolShortName() != null) {
                request.setInstitutionName(requestDTO.getSchoolShortName());
            }
            if (requestDTO.getAddress() != null) {
                Address address = new Address();
                if (requestDTO.getAddress().getEmirate() != null) {
                    address.setEmirate(requestDTO.getAddress().getEmirate());
                }
                if (requestDTO.getAddress().getCity() != null) {
                    address.setCity(requestDTO.getAddress().getCity());
                }
                if (requestDTO.getAddress().getStreet() != null) {
                    address.setStreet(requestDTO.getAddress().getStreet());
                }
                if (requestDTO.getAddress().getHouseNo() != null) {
                    address.setFlat(requestDTO.getAddress().getHouseNo());
                }

                request.setAddress(address);

            }
            if (requestDTO.getDocumentId() != null) {
                request.setDocumentId(requestDTO.getDocumentId());
            }
            if (requestDTO.getRequestChannel() != null) {
                request.setChannel(requestDTO.getRequestChannel().getCbtType());
            }


            JAXBElement<CardRefundCreationRequest> jaxbRequest = factory.createCardRefundCaseCreation(request);
            log.info("Prepared SOAP Request :{}", jaxbRequest);
            JAXBElement<CardRefundCreationResponse> soapResponse =
                    (JAXBElement<CardRefundCreationResponse>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(ADMIN_REFUND_REPLACE, jaxbRequest, withSoapAction("cardRefundCaseCreation"));
            log.debug("Received SOAP response for confirmCardRefund: {}", soapResponse.getValue());
            if (!Long.valueOf(0).equals(soapResponse.getValue().getResponseCode())) {
                throw new CbtIntegrationException(null, null, getErrorCode(soapResponse.getValue().getResponseCode()), soapResponse.getValue().getResponseDescription());
            }
            CardRefundCreationResponse responseDTO = new CardRefundCreationResponse();
            responseDTO.setResponseCode(soapResponse.getValue().getResponseCode());
            responseDTO.setResponseDescription(soapResponse.getValue().getResponseDescription());
            responseDTO.setRtaReferenceNumber(soapResponse.getValue().getRtaReferenceNumber());
            responseDTO.setRefundReferenceId(soapResponse.getValue().getRefundReferenceId());
            responseDTO.setCardApplicationNumber(soapResponse.getValue().getCardApplicationNumber());
            log.info("confirmCardRefund successful for NolCardId: {}", requestDTO.getNolCardId());
            return assembler.getResponse(responseDTO, MessageKeyEnum.SUCCESS, null);

        } catch (SoapFaultClientException ex) {
            log.error("SOAP Fault during confirmCardRefund for NolCardId: {}", requestDTO.getNolCardId(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString())
                    .map(code -> code.getErrorCode().toString())
                    .orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            String exceptionRefId = null;
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            if (faultInfo != null) {
                exceptionRefId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {
                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum error = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(error.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exceptionRefId, violations, faultCode, faultMessage);
        }
    }

    @Override
    public TransactionalAppResponseDTO<SubmitCardReplacementResponseDTO> submitReplacement(LanguageEnum
                                                                                                   language, CardActionEnum action, SubmitCardReplacementRequestDTO requestDTO) {
        try {
            CardReplacementCreationForDSGV2Request request = new CardReplacementCreationForDSGV2Request();
            request.setCardTagId(Long.valueOf(requestDTO.getNolCardId()));
            MobileNumberDTO mobile = requestDTO.getMobileNumber();
            String fullMobileNumber = mobile.getCountryCode().replace("+", "") + mobile.getAreaCode() + mobile.getNumber();
            request.setMobileNumber(fullMobileNumber);
            MobileNumberDTO alternetmobile = requestDTO.getMobileNumber();
            String fullAlternetMobileNumber = alternetmobile.getCountryCode().replace("+", "") + alternetmobile.getAreaCode() + alternetmobile.getNumber();
            request.setAlternateMobileNumber(fullAlternetMobileNumber);
            request.setDocumentId(null);
            request.setEmail(requestDTO.getEmail());
            Address address = new Address();
            address.setCity(requestDTO.getAddress().getCity());
            address.setEmirate(requestDTO.getAddress().getEmirate());
            address.setFlat(requestDTO.getAddress().getHouseNo());
            address.setStreet(requestDTO.getAddress().getStreet());
            request.setAddress(address);
            request.setEmiratesId(null);
            request.setConcessionDocument(null);
            request.setPhoto(null);
            request.setPin(requestDTO.getPin());
            request.setChannel(null);
            request.setReason(requestDTO.getReason());
            request.setIsCardRetained(null);
            request.setLocationId(null);
            request.setBusinessEntityId(null);
            request.setReferenceId(requestDTO.getReferenceId());
            request.setLocationId(null);
            request.setDocumentType(null);
            request.setIdentityCheckPassed(requestDTO.getIdentityCheckPassed().toString());
            request.setOnBehalfCheckPassed(requestDTO.getOnBehalfCheckPassed().toString());
            request.setGuardianDocument(null);
            request.setAuthConcessionExpireDate(null);
            request.setIsicEnabled(0);//is it isicCheckPassed
            request.setIsicInfo(null);
            request.setMobileUserId(null);
            request.setAirwayBillNumber(null);
            request.setPaymentChannel(null);
            request.setDocumentId(requestDTO.getDocumentNumber());
            request.setDocumentType(DocumentType.valueOf(requestDTO.getDocumentType().getCbtType()));


            JAXBElement<CardReplacementCreationForDSGV2Request> jaxbRequest = factory.createCardReplacementCreationForDSG(request);
            log.info("Prepared SOAP Request :{}", jaxbRequest);
            JAXBElement<CardReplacementCreationForDSGV2Response> soapResponse =
                    (JAXBElement<CardReplacementCreationForDSGV2Response>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(ADMIN_REFUND_REPLACE, jaxbRequest, withSoapAction("cardReplacementCreationForDSG"));
            log.info("Received SOAP response for submitReplacement: {}", soapResponse.getValue());
            CardReplacementCreationForDSGV2Response responseDTO = new CardReplacementCreationForDSGV2Response();
            if (!Long.valueOf(0).equals(soapResponse.getValue().getResponseCode())) {
                throw new CbtIntegrationException(null, null, getErrorCode(soapResponse.getValue().getResponseCode()), soapResponse.getValue().getResponseDescription());
            }
            return assembler.getResponse(responseDTO, MessageKeyEnum.SUCCESS, requestDTO.getTransactionId());// if u r sending transacionid update it here
        } catch (SoapFaultClientException ex) {
            log.error("SOAP Fault during submitReplacement for NolCardId: {}", requestDTO.getNolCardId(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString())
                    .map(code -> code.getErrorCode().toString())
                    .orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            String exceptionRefId = null;
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            if (faultInfo != null) {
                exceptionRefId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {
                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum error = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(error.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exceptionRefId, violations, faultCode, faultMessage);
        }

    }

    @Override
    public TransactionalAppResponseDTO<ConfirmCardReplacementResponseDTO> confirmReplacement
            (LanguageEnum language, CardActionEnum action, ConfirmCardReplacementRequestDTO requestDTO) {
        try {
            CardReplacementPaymentFeedbackForDSGRequest request = new CardReplacementPaymentFeedbackForDSGRequest();
            if (requestDTO != null) {
                if (requestDTO.getRequestChannel() != null)
                    request.setRequestChannel(requestDTO.getRequestChannel().getCbtType());
                if (requestDTO.getRequestChannel() != null){
                    ReplacementPaymentParameters replacementPaymentParameters = new ReplacementPaymentParameters();
                    replacementPaymentParameters.setRequestChannel(requestDTO.getRequestChannel().getCbtType());
                    request.setPaymentParameters(replacementPaymentParameters);
                }
                if (requestDTO.getReferenceId() != null)
                    request.setReferenceId(requestDTO.getReferenceId());
            }
            List<PaymentParameter> paymentParameters = new ArrayList<>();
            if (requestDTO != null && requestDTO.getPaymentParameters() != null) {
                for (Map.Entry<String, String> params : requestDTO.getPaymentParameters().entrySet()) {
                    PaymentParameter paymentParam = new PaymentParameter();
                    paymentParam.setKey(params.getKey());
                    paymentParam.setValue(params.getValue());
                    paymentParameters.add(paymentParam);
                }
            }
            JAXBElement<CardReplacementPaymentFeedbackForDSGRequest> jaxbRequest = factory.createCardReplacementPaymentFeedbackForDSG(request);
            log.info("Prepared SOAP Request :{}", jaxbRequest);
            @SuppressWarnings("unchecked")
            CardReplacementPaymentFeedbackForDSGResponse soapResponse = ((JAXBElement<CardReplacementPaymentFeedbackForDSGResponse>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(ADMIN_REFUND_REPLACE, jaxbRequest, withSoapAction("cardReplacementPaymentFeedbackForDSG"))).getValue();
            log.debug("Received SOAP response for confirmReplacement: {}", soapResponse);
            if (!Long.valueOf(0).equals(soapResponse.getResponseCode())) {
                throw new CbtIntegrationException(null, null, getErrorCode(soapResponse.getResponseCode()), soapResponse.getResponseDescription());
            }
            ConfirmCardReplacementResponseDTO responseDTO = new ConfirmCardReplacementResponseDTO();
            if (responseDTO.getRefundReferenceId() != null)
                responseDTO.setRefundReferenceId(soapResponse.getRefundReferenceId());
            if (responseDTO.getRtaReferenceNumber() != null)
                responseDTO.setRtaReferenceNumber(soapResponse.getRtaReferenceNumber());
            if (responseDTO.getRefundReferenceId() != null)
                responseDTO.setRefundReferenceId(soapResponse.getRefundReferenceId());
            if (responseDTO.getCardApplicationNumber() != null)
                responseDTO.setCardApplicationNumber(soapResponse.getCardApplicationNumber());
            return assembler.getResponse(responseDTO, MessageKeyEnum.SUCCESS, null);
        } catch (SoapFaultClientException ex) {
            assert requestDTO != null;
            log.error("SOAP Fault during confirmReplacement for ReferenceId: {}", requestDTO.getReferenceId(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString())
                    .map(code -> code.getErrorCode().toString())
                    .orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            String exceptionRefId = null;
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            if (faultInfo != null) {
                exceptionRefId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {
                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum error = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(error.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exceptionRefId, violations, faultCode, faultMessage);
        }
    }

    @Override
    public TransactionalAppResponseDTO<CardPaymentParamsResponseDTO> getPaymentParams(LanguageEnum
                                                                                              language, CardPaymentParamsRequestDTO requestDTO) {
        try {
            InitiateReplacePCardAppPaymentRequest request = new InitiateReplacePCardAppPaymentRequest();
            if (requestDTO != null) {
                if (requestDTO.getReferenceId() != null)
                    request.setReferenceId(requestDTO.getReferenceId());
                request.setBusinessEntityId(requestDTO.getBeId());
                if (requestDTO.getPaymentMeansType() != null && requestDTO.getPaymentMeansType().getCbtType() != null)
                    request.setPaymentChannel(ReplacementPaymentChannel.valueOf(requestDTO.getPaymentMeansType().getCbtType()));
                if (requestDTO.getRequestChannel() != null)
                    request.setChannel(requestDTO.getRequestChannel().getCbtType());
            }

            JAXBElement<InitiateReplacePCardAppPaymentRequest> jaxbRequest = factory.createInitiateReplacePCardAppPayment(request);
            @SuppressWarnings("unchecked")
            InitiateReplacePCardAppPaymentResponse soapResponse =
                    ((JAXBElement<InitiateReplacePCardAppPaymentResponse>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(jaxbRequest)).getValue();
            log.info("Received SOAP response for getPaymentParams: {}", soapResponse);
            CardPaymentParamsResponseDTO responseDTO = new CardPaymentParamsResponseDTO();

            if (responseDTO.getTotalAmount() != null)
                responseDTO.setTotalAmount(soapResponse.getTotalAmount());
            if (responseDTO.getPaymentMeansType() != null)
                responseDTO.setPaymentMeansType(PaymentMeansTypeEnum.fromCbtType(soapResponse.getPaymentParameters().getPaymentChannel().toString()));
            Map<String, String> paymentParameters = new HashMap<>();
            if (soapResponse.getPaymentParameters() != null && soapResponse.getPaymentParameters().getParameter() != null) {
                for (PaymentParameter param : soapResponse.getPaymentParameters().getParameter()) {
                    paymentParameters.put(param.getKey(), param.getValue());
                }
            }
            if (responseDTO.getPaymentParameters() != null)
                responseDTO.setPaymentParameters(paymentParameters);

            return assembler.getResponse(responseDTO, MessageKeyEnum.SUCCESS, null);
        } catch (SoapFaultClientException ex) {
            assert requestDTO != null;
            log.error("SOAP Fault during getPaymentParams for ReferenceId: {}", requestDTO.getReferenceId(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString())
                    .map(code -> code.getErrorCode().toString())
                    .orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            String exceptionRefId = null;
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            if (faultInfo != null) {
                exceptionRefId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {
                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum error = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(error.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exceptionRefId, violations, faultCode, faultMessage);
        }
    }

    private WebServiceMessageCallback withSoapAction(String soapAction) {
        return message -> {
            ((SoapMessage) message).setSoapAction(soapAction);
        };

    }

    private String getErrorCode(Long responseCode) {
        return CbtMessageKeyEnum.fromCbtCode(String.valueOf(responseCode))
                .map(e -> String.valueOf(e.getErrorCode()))
                .orElse(CbtMessageKeyEnum.INTERNAL_SYSTEM_ERROR.getErrorCode().toString());
    }
}