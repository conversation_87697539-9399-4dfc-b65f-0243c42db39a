package com.paycraft.rta.abt.cam.cbt_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.NationalityDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.ArtWorkDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.CountryDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.*;
import com.paycraft.rta.abt.cam.common.domain.enums.ConcessionTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.ServiceClassEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface UiListMapper {

    CamMstCbtArtWork mapToCamMstCbtArtWork(ArtWorkDTO artWorkDTO);

    @Mapping(target = "refKey", expression = "java(com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_resources.ListType.AREA_CODE.name())")
    @Mapping(target = "refValue", source = "response")
    CamMstCbtReferenceData mapAreaCodeToReferenceData(String response);

    @Mapping(target = "refKey", expression = "java(com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_resources.ListType.OFFICE_AREA_CODE.name())")
    @Mapping(target = "refValue", source = "response")
    CamMstCbtReferenceData mapOfficeAreaCodeToReferenceData(String response);
    
    CamMstCbtCountryCode mapToCamMstCbtCountryCode(CountryDTO response);

    CamMstCbtConcessionType mapToCamMstCbtConcessionType(ConcessionTypeEnum response);

    CamMstCbtServiceClass mapToCamMstCbtServiceClass(ServiceClassEnum response);

    CamMstCbtTitle mapToCamMstCbtTitle(ServiceClassEnum response);

    @Mapping(target = "refKey", expression = "java(com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_resources.ListType.EMIRATE.name())")
    @Mapping(target = "refValue", source = "response")
    CamMstCbtReferenceData mapEmiratesToReferenceData(String response);

    @Mapping(target = "cbtNameEn", source = "nameEn")
    @Mapping(target = "cbtNameAr", source = "nameAr")
    @Mapping(target = "gdrfaIsoCode", source = "iso3Code")
    CamMstCbtNationality mapToCamMstCbtNationality(NationalityDTO response);
}
