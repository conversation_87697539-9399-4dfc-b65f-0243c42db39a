package com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.WeightDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.ContactInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrackShipmentResponseDTO {
    private String trackingNumber;
    private String trackingReferenceNo;
    private ContactInfoDTO sender;
    private ContactInfoDTO receiver;
    private StatusDTO lastStatus;
    private List<EventDTO> events;
    private WeightDTO weight;

}
