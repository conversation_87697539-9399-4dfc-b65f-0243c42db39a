package com.paycraft.rta.abt.cam.common.domain.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "CAM_ISIC_SERIAL_NUMBER_DETAILS")
public class IsicSerialNumberDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SEQ_ISIC_BATCH_ID")
    @SequenceGenerator(name = "SEQ_ISIC_BATCH_ID", sequenceName = "SEQ_ISIC_BATCH_ID", allocationSize = 1)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "ISIC_SERIAL_NUMBER", length = 50, nullable = false)
    private String isicSerialNumber;

    @Column(name = "BATCH_ID", length = 50, nullable = false)
    private String batchId;

    @Column(name = "TRANSACTION_ID", length = 50, nullable = false)
    private String transactionId;

    @Column(name = "TRANSACTION_REF_ID", nullable = false)
    private String  transactionRefId;

    @Column(name = "STATUS", length = 20)
    private String status;

    @Column(name = "AUTO_VALIDATE_REF_NO")
    private String autoValidateRefNo;

    @CreationTimestamp
    @Column(name = "CREATED_DATE", updatable = false)
    private Timestamp createdDate;

    @Column(name = "CREATED_BY", length = 50)
    private String createdBy;

    @Column(name = "UPDATED_DATE")
    private Timestamp updatedDate;

    @Column(name = "UPDATED_BY", length = 50)
    private String updatedBy;
}
