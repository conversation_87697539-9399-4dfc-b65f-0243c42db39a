package com.paycraft.rta.abt.cam.cbt_integration.domain.constants;

public class EndpointConstants {

    public static final String DONATION_CONFIG = "/donation/data";
    public static final String UI_DATA_LIST =  "/ui-list/data";
    public static final String IMAGE_BASE64 = "/image";
    private static final String CARD = "/v1/card";

    public static final String TOPUP_AMOUNT_OPTIONS = "/topup/amount-options";
    public static final String TOPUP_VALIDATE = "/topup/validate";
    public static final String TOPUP_CONFIRM = "/topup/confirm";
    public static final String TOPUP_SUBMIT = "/topup/submit";
    private static final String GET ="/get-card";
    public static final String RESET_PIN="/reset/card-pin";
    public static final String FIND_KEY="/find/card-keys";
    public static final String GENERAL_HISTORY="/card/general-history";
    public static final String CARD_INFO ="/card/info";
    public static final String TRAVEL_HISTORY = "/card/travel/history";
    public static final String CARD_LIST = "/card/list";
    // Travel Product Renewal APIs
    public static final String TRAVEL_PASS_INFO = "/product/travel-pass/info";
    public static final String TRAVEL_PASS_RENEW_SUBMIT = "/product/renew/submit";
    public static final String TRAVEL_PASS_RENEW_CONFIRM = "/product/renew/confirm";
    public static final String TRAVEL_PASS_RENEW_VALIDATE = "/product/renew/validate";
    public static final String TRAVEL_PASS_RENEW_REVIEW = "/product/renew/review";
    public static final String LINK = "/card/link";
    public static final String VALIDATE = "/validate";
    public static final String DELINK = "/card/de-link";

    //card replacement and renewal
    public static final String CARD_ACTION_SUBMIT = CARD + "/{action}/submit";
    public static final String CARD_ACTION_CONFIRM = CARD + "/{action}/confirm";
    public static final String PAYMENT_PARAMS = CARD + "/payment-params";
    //card refund
    public static final String OVER_CHARGE=CARD+"/refund/over-charge";
    public static final String REFUND_CONFIRM=CARD+"/refund/confirm";
    public static final String FILE_UPLOAD = "/upload";
    public static final String CARD_REGISTER_REQUEST_CONFIRM="/card/register/request/response";
    public static final String CARD_REGISTER_REQUEST_REVIEW = "/card/register/request/review";
    public static final String CARD_REGISTER_REQUEST_SUBMIT = "/card/register/request/submit";
    public static final String CARD_REGISTER_REQUEST_EXCHANGE_HOUSE_CONFIRM ="/card/exchange-house/register/request/confirm";
    public static final String CARD_REGISTER_REQUEST_VALIDATE = "/card/register/request/validate";
    public static final String USER_PROFILE = CARD+"/user-profile";
    public static final String UPDATE = USER_PROFILE+"/update";

    public static final String PCARD_REVIEW = "/review";
    public static final String PCARD_CONFIRM="/confirm";
    public static final String PCARD_SUBMIT ="/submit";
    public static final String PCARD_PAYMENT_PARAMS ="/payment-params";
    public static final String PCARD_TRACK = CARD+"/personalized/request/track";


}