package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.topup;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TopupConfirmRequestDTO extends RequestDTO {
    @NotNull
    private Map<String,String> paymentParameters;
    @NotNull
    private PaymentMeansTypeEnum paymentMeansType;
}
