//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for isicInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="isicInfo"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ISICNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="institutionName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ISICHolderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "isicInfo", namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/2_0", propOrder = {
    "isicNumber",
    "institutionName",
    "isicHolderId"
})
public class IsicInfo {

    @XmlElement(name = "ISICNumber")
    protected String isicNumber;
    protected String institutionName;
    @XmlElement(name = "ISICHolderId")
    protected int isicHolderId;

    /**
     * Gets the value of the isicNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getISICNumber() {
        return isicNumber;
    }

    /**
     * Sets the value of the isicNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setISICNumber(String value) {
        this.isicNumber = value;
    }

    /**
     * Gets the value of the institutionName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInstitutionName() {
        return institutionName;
    }

    /**
     * Sets the value of the institutionName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInstitutionName(String value) {
        this.institutionName = value;
    }

    /**
     * Gets the value of the isicHolderId property.
     * 
     */
    public int getISICHolderId() {
        return isicHolderId;
    }

    /**
     * Sets the value of the isicHolderId property.
     * 
     */
    public void setISICHolderId(int value) {
        this.isicHolderId = value;
    }

}
