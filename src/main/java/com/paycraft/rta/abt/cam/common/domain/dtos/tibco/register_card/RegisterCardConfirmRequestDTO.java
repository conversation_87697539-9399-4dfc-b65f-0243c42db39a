package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegisterCardConfirmRequestDTO extends RequestDTO {
    private PaymentMeansTypeEnum paymentMeansType;
    @NotNull
    private Map<String,String> paymentParameters;

    private BigDecimal totalAmount;
}
