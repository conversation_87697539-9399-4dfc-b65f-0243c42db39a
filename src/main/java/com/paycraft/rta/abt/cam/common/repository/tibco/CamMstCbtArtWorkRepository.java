package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtArtWork;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtArtWorkId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CamMstCbtArtWorkRepository extends JpaRepository<CamMstCbtArtWork, CamMstCbtArtWorkId> {

    List<CamMstCbtArtWork> findByLanguage(String languageCode);
}
