package com.paycraft.rta.abt.cam.common.domain.entities.tibco;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "CAM_MST_CBT_REFERENCE_DATA")
public class CamMstCbtReferenceData {

    @Id
    @Column(name = "ID")
    private Long id; //  areaCode, officeAreaCode -> values

    @Column(name = "REF_KEY", length = 50)
    private String refKey;  // area, officeAreaCode -> name

    @Column(name = "REF_VALUE", length = 50)
    private String refValue;

    @Column(name = "CREATED_BY", length = 50)
    private String createdBy;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "UPDATED_BY", length = 50)
    private String updatedBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;
}
