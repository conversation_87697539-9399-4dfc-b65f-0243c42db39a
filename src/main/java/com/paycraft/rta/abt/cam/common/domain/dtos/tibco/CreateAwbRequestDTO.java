package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.WeightDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.AccountDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.ValuesDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateAwbRequestDTO {
    private WeightDTO weight; // Mandatory
    private EmxPersonDetailsDTO shipper; // few Mandatory
    private EmxPersonDetailsDTO consignee;  // few Mandatory
    private AccountDTO account; // Mandatory
    private EmxProductCodeEnum productCode; // Mandatory (EMX-International,ETOEPARCEL,International,Domestic) TODO : convert to enum
    private ExmServiceTypeEnum serviceType; // Mandatory (Domestic,International,EMS, None,SameDay,PrimeRegistered,PrimeTracked,PrimeExpres,Registered,Parcel,EconomyParcel,Return, Standard) TODO : Enum
    private EmxPrintTypeEnum printType;   // Mandatory (LabelOnly,AWBOnly,AWBAndLabel,None)
    private Integer numberOfPieces;  // Mandatory
    private EmxDeliveryTypeEnum deliveryType;  // Mandatory (DoorToDoor,Counter, PUDO)
    private EmxContentTypeEnum contentType; // Mandatory (Document,NonDocument)
    private ValuesDTO coDAmount;  // few Mandatory
}
