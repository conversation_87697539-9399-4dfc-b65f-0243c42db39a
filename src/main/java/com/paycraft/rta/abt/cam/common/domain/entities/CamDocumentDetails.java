package com.paycraft.rta.abt.cam.common.domain.entities;

import com.paycraft.rta.abt.cam.common.domain.constants.AppConstants;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
public class CamDocumentDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "doc_seq_gen")
    @SequenceGenerator(name = "doc_seq_gen", sequenceName = "CAM_MASTER.SEQ_CAM_DOCUMENT_DETAILS", allocationSize = 1)
    @Column(name = "DOCUMENT_REFERENCE_ID")
    private Long documentReferenceId;

    @Column(name = "DOCUMENT_GROUP_REFERENCE_ID")
    private String documentGroupReferenceId;

    @Column(name = "DOCUMENT_ID")
    private String documentId;

    @Column(name = "DOCUMENT_TYPE")
    private Short documentType;

    @Lob
    @Column(name = "DOCUMENT_FILE")
    private byte[] documentFile;

    @Column(name = "DOCUMENT_NAME")
    private String documentName;

    @Column(name = "FILE_CHECKSUM")
    private String fileChecksum;

    @Column(name = "FILE_SIZE")
    private Integer fileSize;

    @Column(name = "FILE_REFERENCE_NO")
    private Long fileReferenceNo;

    @Column(name = "TRANSACTION_ID")
    private String transactionId;

    @Column(name = "REQUEST_CHANNEL")
    private String requestChannel;

    @Column(name = "IAM_USER_ID")
    private String iamUserId;

    @Column(name = "UAE_PASS_USER_ID")
    private String uaePassUserId;

    @Column(name = "ABT_ACCOUNT_ID")
    private String abtAccountId;

    @Column(name = "GUEST_COUNTRY_CODE")
    private String guestCountryCode;

    @Column(name = "GUEST_AREA_CODE")
    private String guestAreaCode;

    @Column(name = "GUEST_NUMBER")
    private String guestNumber;

    @Column(name = "GUEST_EMAIL")
    private String guestEmail;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @PrePersist
    public void prePersist() {
        this.createdDate = LocalDateTime.now();
        this.updatedDate = LocalDateTime.now();
        this.createdBy = AppConstants.DEFAULT_CREATOR;
        this.updatedBy = AppConstants.DEFAULT_CREATOR;
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedDate = LocalDateTime.now();
        this.updatedBy = AppConstants.DEFAULT_CREATOR;
    }
}
