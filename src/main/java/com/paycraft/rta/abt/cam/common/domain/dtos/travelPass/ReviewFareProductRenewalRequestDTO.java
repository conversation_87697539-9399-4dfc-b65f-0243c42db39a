package com.paycraft.rta.abt.cam.common.domain.dtos.travelPass;


import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.GuestUserInfo;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.RequestChannelEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReviewFareProductRenewalRequestDTO implements Serializable {

    private long beId;
    @NotNull
    private RequestChannelEnum requestChannel;
    @NotBlank
    private String transactionId;

    private String iamUserId;

    private String uaePassUserId;

    private String abtAccountId;
    @Valid
    private GuestUserInfo guestUserInfo;
    @NotBlank
    private String nolCardId;

    private String cardToken;

    private Map<String,String > attributes;

    private PaymentMeansTypeEnum paymentMeansType;
    private String productCode;
    private String productSubCode;
    private LocalDateTime activationDate;
}