package com.paycraft.rta.abt.cam.common.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.CamOtpDetails;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamPcardUserApplicationRecord;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.OtpStatusEnum;
import com.paycraft.rta.abt.cam.common.exception.ValidationException;
import com.paycraft.rta.abt.cam.common.repository.OtpDetailsRepository;
import com.paycraft.rta.abt.cam.common.repository.tibco.CamPcardUserAppRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum.CHECKS_NOT_MATCHED;
import static com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum.RESOURCE_NOT_FOUND;

@Component
public class PCardServiceImpl {

    private static final Integer ADULT_START_AGE = 18;
    private static final Integer ADULT_END_AGE = 60;
    public static final String MOBILE_OTP_IDENTIFIER = "mobileOtpIdentifier";
    public static final String EMAIL_OTP_IDENTIFIER = "emailOtpIdentifier";
    public static final String ALTERNATE_MOBILE_OTP_IDENTIFIER = "alternateMobileOtpIdentifier";
    public static final String FALSE = "0";

    @Autowired
    OtpDetailsRepository otpDetailsRepository;

    @Autowired
    CamPcardUserAppRecordRepository camPcardUserAppRecordRepository;

    public  void validateAttributes(
            Map<String, String> attributes,
            String mobileNumber,
            String alternateMobileNumber,
            String email
    ) {
        String mobileOtp = attributes.getOrDefault(MOBILE_OTP_IDENTIFIER, "").trim();
        String alternateMobileOtp = attributes.getOrDefault(ALTERNATE_MOBILE_OTP_IDENTIFIER, "").trim();
        String emailOtp = attributes.getOrDefault(EMAIL_OTP_IDENTIFIER, "").trim();

        if (!mobileOtp.isEmpty()) {
            CamOtpDetails mobileOtpDetails = otpDetailsRepository.findById(Long.valueOf(mobileOtp)).orElseThrow(
                    () -> new ValidationException(List.of(ValidationResponseDTO.of(
                            ErrorCodesEnum.INVALID_OTP.getErrorCode(), ErrorCodesEnum.INVALID_OTP.getMessage()
                    )))
            );

            if(!mobileOtpDetails.getStatus().equalsIgnoreCase(OtpStatusEnum.VALIDATED.getId())){
                throw new ValidationException(List.of(
                        ValidationResponseDTO.of(
                                ErrorCodesEnum.OTP_NOT_VALIDATED.getErrorCode(),
                                ErrorCodesEnum.OTP_NOT_VALIDATED.getMessage()
                        )
                ));
            }

            if (!mobileNumber.equals(mobileOtpDetails.getMobileNumber())) {
                throw new ValidationException(List.of(
                        ValidationResponseDTO.of(
                                ErrorCodesEnum.MOBILE_NUMBER_NOT_VALIDATED.getErrorCode(),
                                ErrorCodesEnum.MOBILE_NUMBER_NOT_VALIDATED.getMessage()
                        )
                ));
            }
        }

        if (!emailOtp.isEmpty()) {
            CamOtpDetails emailOtpDetails = otpDetailsRepository.findById(Long.valueOf(emailOtp)).orElseThrow(
                    () -> new ValidationException(List.of(ValidationResponseDTO.of(
                            ErrorCodesEnum.INVALID_OTP.getErrorCode(), ErrorCodesEnum.INVALID_OTP.getMessage()
                    )))
            );

            if(!emailOtpDetails.getStatus().equalsIgnoreCase(OtpStatusEnum.VALIDATED.getId())){
                throw new ValidationException(List.of(
                        ValidationResponseDTO.of(
                                ErrorCodesEnum.OTP_NOT_VALIDATED.getErrorCode(),
                                ErrorCodesEnum.OTP_NOT_VALIDATED.getMessage()
                        )
                ));
            }

            if (!email.equals(emailOtpDetails.getEmailId())) {
                throw new ValidationException(List.of(
                        ValidationResponseDTO.of(
                                ErrorCodesEnum.EMAIL_NOT_VALIDATED.getErrorCode(),
                                ErrorCodesEnum.EMAIL_NOT_VALIDATED.getMessage()
                        )
                ));
            }


        }

        if (!alternateMobileOtp.isEmpty()) {
            CamOtpDetails alternateMobileOtpDetails = otpDetailsRepository.findById(Long.valueOf(alternateMobileOtp)).orElseThrow(
                    () -> new ValidationException(List.of(ValidationResponseDTO.of(
                            ErrorCodesEnum.INVALID_OTP.getErrorCode(), ErrorCodesEnum.INVALID_OTP.getMessage()
                    )))
            );

            if(!alternateMobileOtpDetails.getStatus().equalsIgnoreCase(OtpStatusEnum.VALIDATED.getId())){
                throw new ValidationException(List.of(
                        ValidationResponseDTO.of(
                                ErrorCodesEnum.OTP_NOT_VALIDATED.getErrorCode(),
                                ErrorCodesEnum.OTP_NOT_VALIDATED.getMessage()
                        )
                ));
            }

            if (!alternateMobileNumber.equals(alternateMobileOtpDetails.getMobileNumber())) {
                throw new ValidationException(List.of(
                        ValidationResponseDTO.of(
                                ErrorCodesEnum.MOBILE_NUMBER_NOT_VALIDATED.getErrorCode(),
                                ErrorCodesEnum.MOBILE_NUMBER_NOT_VALIDATED.getMessage()
                        )
                ));
            }
        }
    }

    public void validatePassedChecks(
            String autoValidationReferenceNumber,
            String concessionCheckPassedId,
            String isicCheckPassedId,
            String identityCheckPassedId,
            String onBehalfCheckPassedId) {

        CamPcardUserApplicationRecord camPcardUserApplicationRecord = camPcardUserAppRecordRepository
                .findByAutoValidationReferenceNumber(autoValidationReferenceNumber)
                .orElseThrow(() -> new ValidationException(
                        List.of(ValidationResponseDTO.of(
                                RESOURCE_NOT_FOUND.getErrorCode(),
                                RESOURCE_NOT_FOUND.getMessage() + ": PCard User Application details"
                        ))
                ));

        if (!camPcardUserApplicationRecord.getConcessionCheckPassed().equalsIgnoreCase(concessionCheckPassedId)
                || !camPcardUserApplicationRecord.getIsicCheckPassed().equalsIgnoreCase(isicCheckPassedId)
                || !camPcardUserApplicationRecord.getIdentityCheckPassed().equalsIgnoreCase(identityCheckPassedId)
                || !camPcardUserApplicationRecord.getOnBehalfCheckPassed().equalsIgnoreCase(onBehalfCheckPassedId)) {
            throw new ValidationException(
                    List.of(ValidationResponseDTO.of(
                            CHECKS_NOT_MATCHED.getErrorCode(),
                            CHECKS_NOT_MATCHED.getMessage()
                    )));
        }
    }

}
