package com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ContactDTO {
    private String name;  // Mandatory
    private String mobileNumber;  // Mandatory (For mobilenumber pleasestart with 05 forUAE mobilenumbers andcountry code forinternational numbers.)
    private String phoneNumber;
    private String emailAddress;  // Mandatory for Consignee
    private String companyName;
    private String altMobileNumber;
    private String altMobileNumber2;
}
