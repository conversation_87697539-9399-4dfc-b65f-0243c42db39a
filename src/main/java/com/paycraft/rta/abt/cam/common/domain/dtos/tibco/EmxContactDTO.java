package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class EmxContactDTO {
    private String name;  // Mandatory
    private String mobileNumber;  // Mandatory (For mobilenumber pleasestart with 05 forUAE mobilenumbers andcountry code forinternational numbers.)
    private String emailAddress;  // Mandatory for Consignee
    private String altMobileNumber;
}
