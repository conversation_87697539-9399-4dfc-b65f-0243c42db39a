package com.paycraft.rta.abt.cam.cbt_integration.controller;


import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.*;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.service.impl.MobileCardEnquiryServiceImpl;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.CardInfoRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.CardInfoResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.RegisteredCardListResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.GeneralHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.TravelHistoryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.TravelHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.*;

@Validated
@RestController
public class MobileCardEnquiryController {

    @Autowired
    MobileCardEnquiryService mobileCardEnquiryService;

    @Autowired
    private TransactionAppResponseDTOAssembler assembler;

    private  static final Logger log = LogManager.getLogger(MobileCardEnquiryController.class);

   /* @PostMapping (FIND_KEY)
    public ResponseEntity<?> findCardKey(@RequestBody @Valid FindKeyDTO requestDTO) {
        try {
            return ResponseEntity.ok( mobileCardEnquiryService.findCardKey(requestDTO.getTagId()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("Error finding card key: " + e.getMessage());
        }
    }*/

    @PostMapping(CARD_LIST)
    @Operation(summary = "Registered Card List", description = "Get Registered Card List")
    public ResponseEntity<TransactionalAppResponseDTO<List<RegisteredCardListResponseDTO>>> registeredCardsList(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody RequestDTO requestDTO) {
        log.info("Request for Registered Card List : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(mobileCardEnquiryService.getCardsList(clientLanguage,requestDTO), MessageKeyEnum.SUCCESS, requestDTO.getTransactionId()));
    }

    @PostMapping(TRAVEL_HISTORY)
    public ResponseEntity<TransactionalAppResponseDTO<TravelHistoryResponseDTO>> fetchTravelHistory(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody TravelHistoryRequestDTO requestDTO) {
        log.info("Request for Get Travel History : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(mobileCardEnquiryService.fetchTravelHistory(clientLanguage,requestDTO), MessageKeyEnum.SUCCESS, requestDTO.getTransactionId()));
    }

    @PostMapping(GENERAL_HISTORY)
    public ResponseEntity<TransactionalAppResponseDTO<GeneralHistoryResponseDTO>> fetchGeneralHistory(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody TravelHistoryRequestDTO requestDTO){
        log.info("Request for Get General History : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(mobileCardEnquiryService.fetchGeneralHistory(clientLanguage,requestDTO), MessageKeyEnum.SUCCESS,requestDTO.getTransactionId()));
    }

    @PostMapping(CARD_INFO)
    @Operation(summary = "Get Card Details", description = "fetch Card Details")
    public ResponseEntity<TransactionalAppResponseDTO<CardInfoResponseDTO>> CardInfo(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody CardInfoRequestDTO requestDTO) {
        log.info("Request for Topup submit : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(mobileCardEnquiryService.getCardInfo(clientLanguage,requestDTO), MessageKeyEnum.SUCCESS, requestDTO.getTransactionId()));
    }

}
