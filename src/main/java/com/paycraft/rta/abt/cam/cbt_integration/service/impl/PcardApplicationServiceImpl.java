package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.GuestUserInfoDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.*;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import com.paycraft.rta.abt.cam.cbt_integration.service.supplier.PCardDetailsSupplierAndEnrichmentHandler;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.PaymentParamsRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.PaymentParamsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.CamDocumentDetails;
import com.paycraft.rta.abt.cam.common.domain.enums.DocumentTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.exception.SoapHeaderException;
import com.paycraft.rta.abt.cam.common.exception.SoapMarshallingException;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.domain.mapper.PCardMapper;
import com.paycraft.rta.abt.cam.cbt_integration.service.PcardApplicationService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.*;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.soap.SOAPEnvelope;
import jakarta.xml.soap.SOAPException;
import jakarta.xml.soap.SOAPHeader;
import jakarta.xml.soap.SOAPMessage;
import jakarta.xml.soap.SOAPPart;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.saaj.SaajSoapMessage;

import java.math.BigDecimal;
import javax.sound.midi.Track;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.P_CARD_APPLICATION;


@Service
@RequiredArgsConstructor
public class PcardApplicationServiceImpl implements PcardApplicationService {

    private static final Logger log = LogManager.getLogger(PcardApplicationServiceImpl.class);


    private final SoapClientRegistry soapClientRegistry;

    private WebServiceTemplate getTemplate() {
        return soapClientRegistry.getTemplate("pCardRegistration");
    }

    @Autowired
    private PCardMapper pCardMapper;

    @Autowired
    private PCardDetailsSupplierAndEnrichmentHandler pCardDetailsSupplierAndEnrichmentHandler;

    private static final String REVIEW_P_CARD_SOAP_ACTION = "reviewPCardApplication";
    private static final String CONFIRM_P_CARD_SOAP_ACTION = "responsePCardApplicationRequest";
    private static final String SUBMIT_P_CARD_SOAP_ACTION = "submitPCardApplication";
    private static final String GET_PAYMENT_PARAMETERS_SOAP_ACTION = "initiatePCardAppPayment";



    @Override
    public TransactionalAppResponseDTO<PcardReviewResponseDTO> reviewPcard(PcardReviewSubmitRequestDTO requestDTO,LanguageEnum clientLanguage) {

        try {

            ObjectFactory objectFactory = new ObjectFactory();
            AcceptLanguages headerObject = new AcceptLanguages();
            headerObject.setLanguage(clientLanguage.getCbtType());


            JAXBContext jaxbContext = JAXBContext.newInstance(AcceptLanguages.class);
            Marshaller marshaller = jaxbContext.createMarshaller();

            // 3. Create the SOAP Header callback
            WebServiceMessageCallback headerCallback = getWebServiceMessageCallback(marshaller, headerObject ,REVIEW_P_CARD_SOAP_ACTION);

            // 4. Create request payload (main SOAP body)
            PCardApplicationRequest requestPayload = pCardMapper.toSoapRequest(requestDTO);
            requestPayload.setRequestChannel(requestDTO.getRequestChannel().getCbtType());
            requestPayload.setDocumentType(
                    DocumentType.valueOf(requestDTO.getDocumentType().getCbtType())
            );
            requestPayload.setDocumentId(requestDTO.getDocumentNumber());
            log.info("ISIC Enabled Request : {} ", requestPayload.getIsicEnabled());
            if (1 == requestPayload.getIsicEnabled()) {
                requestPayload.setIsicInfo(pCardDetailsSupplierAndEnrichmentHandler.getIsicInformation.apply(requestDTO.getAutoValidationReferenceNumber()));
            }

            Optional.ofNullable(requestDTO.getDocumentGroupReferenceId()).ifPresent(documentGrpRefId -> {
                List<CamDocumentDetails> camDocumentDetails = pCardDetailsSupplierAndEnrichmentHandler.getDocumentFileReferenceDetails.apply(documentGrpRefId);
                if(camDocumentDetails != null && !camDocumentDetails.isEmpty()) {
                    camDocumentDetails.forEach(camDocumentDetail -> {
                        if (DocumentTypeEnum.SPONSOR_SUPPORTING_DOCUMENT.getId().equals(camDocumentDetail.getDocumentType())) {
                            UploadFileReference uploadFileReference = new UploadFileReference();
                            uploadFileReference.setFileReferenceNo(camDocumentDetail.getFileReferenceNo());
                            requestPayload.setGuardianDocument(uploadFileReference);
                        }

                        if (DocumentTypeEnum.POD_SUPPORTING_DOCUMENT.getId().equals(camDocumentDetail.getDocumentType()) || DocumentTypeEnum.STUDENT_SUPPORTING_DOCUMENT.getId().equals(camDocumentDetail.getDocumentType())) {
                            UploadFileReference uploadFileReference = new UploadFileReference();
                            uploadFileReference.setFileReferenceNo(camDocumentDetail.getFileReferenceNo());
                            requestPayload.setConcessionDocument(uploadFileReference);
                        }
                    });
                }
            });
            JAXBElement<PCardApplicationRequest> jaxbRequest = objectFactory.createPCardApplicationReviewRequest(requestPayload);


            // 5. Send request with header
            @SuppressWarnings("unchecked")
            JAXBElement<PaymentAmountList> responseWrapper =
                    (JAXBElement<PaymentAmountList>) getTemplate().marshalSendAndReceive(
                            P_CARD_APPLICATION,
                            jaxbRequest,
                            headerCallback
                    );


            PaymentAmountList response = responseWrapper.getValue();


            log.info("Request for Personalised card Review : {}", requestDTO.getTransactionId());
            PcardReviewResponseDTO pcardReviewResponseDTO = new PcardReviewResponseDTO();
            if (response.getLineItems() != null) {
                List<LineItemResponseDTO> mappedLineItems = response.getLineItems().stream()
                        .map(lineItem -> {
                            LineItemResponseDTO itemDto = new LineItemResponseDTO();
                            itemDto.setName(lineItem.getName());   // assuming `LineItem` has `getName()`
                            itemDto.setValue(lineItem.getValue().toString()); // assuming `LineItem` has `getValue()`
                            return itemDto;
                        })
                        .collect(Collectors.toList());

                pcardReviewResponseDTO.setLineItems(mappedLineItems);
            }

            pcardReviewResponseDTO.setTotalAmount(response.getTotalAmount());


            return TransactionalAppResponseDTO.of(requestDTO.getTransactionId(), MessageKeyEnum.SUCCESS.getCode(), MessageKeyEnum.SUCCESS.getMessage(), null, pcardReviewResponseDTO);

        }   catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred with fault code in reviewPcard {}",ex.getFaultCode());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex,StandardFaultInfo.class);
            String exceptionRefrenceId = null;

            if (faultInfo != null) {
                exceptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exceptionRefrenceId , violations ,faultCode, faultMessage);
        } catch (JAXBException e) {
            throw new SoapMarshallingException("Failed to marshal SOAP request object", e);
        }
    }


    @Override
    public TransactionalAppResponseDTO<PcardConfirmResponseDTO> confirmPcard(LanguageEnum clientLanguage, PcardConfirmRequestDTO requestDTO) {
        try {

            ObjectFactory objectFactory = new ObjectFactory();
            AcceptLanguages headerObject = new AcceptLanguages();
            headerObject.setLanguage(clientLanguage.getCbtType());


            JAXBContext jaxbContext = JAXBContext.newInstance(AcceptLanguages.class);
            Marshaller marshaller = jaxbContext.createMarshaller();

            // 3. Create the SOAP Header callback
            WebServiceMessageCallback headerCallback = getWebServiceMessageCallback(marshaller, headerObject , CONFIRM_P_CARD_SOAP_ACTION);

            PaymentParameters paymentParameters = new PaymentParameters();
            paymentParameters.setPaymentChannel(requestDTO.getPaymentMeansType().getCbtType());
            paymentParameters.setRequestChannel(requestDTO.getRequestChannel().getCbtType());
            for(Map.Entry<String, String> entry : requestDTO.getPaymentParameters().entrySet()){
                PaymentParameter paymentParameter = new PaymentParameter();
                paymentParameter.setKey(entry.getKey());
                paymentParameter.setValue(entry.getValue());
                paymentParameters.getParameter().add(paymentParameter);
            }
            JAXBElement<PaymentParameters> jaxbRequest = objectFactory.createPaymentResponse(paymentParameters);

            @SuppressWarnings("unchecked")
            JAXBElement<PCardApplicationConfirmation> responseWrapper =
                    (JAXBElement<PCardApplicationConfirmation>) getTemplate().marshalSendAndReceive(
                            P_CARD_APPLICATION,
                            jaxbRequest,
                            headerCallback
                    );

            PCardApplicationConfirmation pCardApplicationConfirmation = responseWrapper.getValue();
            PcardConfirmResponseDTO pcardConfirmResponseDTO = new PcardConfirmResponseDTO();
            pcardConfirmResponseDTO.setApplicationReferenceNumber(pCardApplicationConfirmation.getReferenceId());


            return TransactionalAppResponseDTO.of(requestDTO.getTransactionId(), pCardApplicationConfirmation.getPaymentStatus() == PaymentStatus.FAIL ? MessageKeyEnum.FAIL.getCode(): MessageKeyEnum.SUCCESS.getCode(), pCardApplicationConfirmation.getPaymentStatus() == PaymentStatus.FAIL ? MessageKeyEnum.FAIL.getMessage(): MessageKeyEnum.SUCCESS.getMessage(), null, pcardConfirmResponseDTO);
        }    catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred with fault code in confirmPcard {}",ex.getFaultCode());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex,StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations ,faultCode, faultMessage);
        } catch (JAXBException e) {
    throw new SoapMarshallingException("Failed to marshal SOAP request object", e);
}
    }

    @Override
    public TransactionalAppResponseDTO<PcardSubmitResponseDTO> submitPcard(LanguageEnum clientLanguage, PcardReviewSubmitRequestDTO requestDTO) {
        try {

            ObjectFactory objectFactory = new ObjectFactory();

            AcceptLanguages headerObject = new AcceptLanguages();
            headerObject.setLanguage(clientLanguage.getCbtType());

            // 2. Create JAXB Marshaller
            JAXBContext jaxbContext = JAXBContext.newInstance(AcceptLanguages.class);
            Marshaller marshaller = jaxbContext.createMarshaller();

            WebServiceMessageCallback headerCallback = getWebServiceMessageCallback(marshaller, headerObject,SUBMIT_P_CARD_SOAP_ACTION);

            // 4. Create request payload (main SOAP body)
            PCardApplicationRequest requestPayload = pCardMapper.toSoapRequest(requestDTO);
            requestPayload.setRequestChannel(requestDTO.getRequestChannel().getCbtType());
            requestPayload.setDocumentType(
                    DocumentType.valueOf(requestDTO.getDocumentType().getCbtType())
            );
            requestPayload.setDocumentId(requestDTO.getDocumentNumber());


            // TODO - Refactor below code
            if (1 == requestPayload.getIsicEnabled()) {
                requestPayload.setIsicInfo(pCardDetailsSupplierAndEnrichmentHandler.getIsicInformation.apply(requestDTO.getAutoValidationReferenceNumber()));
            }

            Optional.ofNullable(requestDTO.getDocumentGroupReferenceId()).ifPresent(documentGrpRefId -> {
                List<CamDocumentDetails> camDocumentDetails = pCardDetailsSupplierAndEnrichmentHandler.getDocumentFileReferenceDetails.apply(documentGrpRefId);
                if(camDocumentDetails != null && !camDocumentDetails.isEmpty()) {
                    camDocumentDetails.forEach(camDocumentDetail -> {
                        if (DocumentTypeEnum.SPONSOR_SUPPORTING_DOCUMENT.getId().equals(camDocumentDetail.getDocumentType())) {
                            UploadFileReference uploadFileReference = new UploadFileReference();
                            uploadFileReference.setFileReferenceNo(camDocumentDetail.getFileReferenceNo());
                            requestPayload.setGuardianDocument(uploadFileReference);
                        }

                        if (DocumentTypeEnum.POD_SUPPORTING_DOCUMENT.getId().equals(camDocumentDetail.getDocumentType()) || DocumentTypeEnum.STUDENT_SUPPORTING_DOCUMENT.getId().equals(camDocumentDetail.getDocumentType())) {
                            UploadFileReference uploadFileReference = new UploadFileReference();
                            uploadFileReference.setFileReferenceNo(camDocumentDetail.getFileReferenceNo());
                            requestPayload.setConcessionDocument(uploadFileReference);
                        }
                    });
                }
            });

            JAXBElement<PCardApplicationRequest> jaxbRequest = objectFactory.createPCardApplicationSubmissionRequest(requestPayload);

            // 5. Send request with header
            @SuppressWarnings("unchecked")
            JAXBElement<PCardApplicationResponse> responseWrapper =
                    (JAXBElement<PCardApplicationResponse>) getTemplate().marshalSendAndReceive(
                            P_CARD_APPLICATION,
                            jaxbRequest,
                            headerCallback
                    );


            PCardApplicationResponse response = responseWrapper.getValue();

            log.info("Request for Personalised card Review : {}", requestDTO.getTransactionId());

            PcardSubmitResponseDTO pcardSubmitResponseDTO = new PcardSubmitResponseDTO();
            pcardSubmitResponseDTO.setIsAutoApproved(YesOrNoEnum.ID_TO_ENUM_MAP.get(response.getIsAutoApproved()));
            pcardSubmitResponseDTO.setLineItems(
                    response.getLineItems().stream().map(
                            lineItemVo -> {
                                return   new LineItemResponseDTO(lineItemVo.getName(),lineItemVo.getValue());
                            }
                    ).toList()
            );
            pcardSubmitResponseDTO.setTotalAmount(response.getTotalAmount());
            pcardSubmitResponseDTO.setPaymentMeansType(requestDTO.getPaymentMeansType());
            pcardSubmitResponseDTO.setConfirmationNote(response.getConfirmationNote());
            pcardSubmitResponseDTO.setApplicationReferenceNumber(response.getReferenceId());

            if (pcardSubmitResponseDTO.getTotalAmount() != null && pcardSubmitResponseDTO.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                PaymentParamsRequestDTO paymentParamsRequestDTO = new PaymentParamsRequestDTO();
                paymentParamsRequestDTO.setPaymentMeansType(requestDTO.getPaymentMeansType());
                paymentParamsRequestDTO.setApplicationReferenceNumber(pcardSubmitResponseDTO.getApplicationReferenceNumber());
                paymentParamsRequestDTO.setRequestChannel(requestDTO.getRequestChannel());
                paymentParamsRequestDTO.setBeId(requestDTO.getBeId());
                paymentParamsRequestDTO.setIamUserId(requestDTO.getIamUserId());
                paymentParamsRequestDTO.setTransactionId(requestDTO.getTransactionId());
                TransactionalAppResponseDTO<PaymentParamsResponseDTO> paymentResponseDTO = fetchPcardPaymentParams(clientLanguage, paymentParamsRequestDTO);
                pcardSubmitResponseDTO.setPaymentParameters(paymentResponseDTO.getData().getPaymentParameters());
            }

            return TransactionalAppResponseDTO.of(requestDTO.getTransactionId(), MessageKeyEnum.SUCCESS.getCode(), MessageKeyEnum.SUCCESS.getMessage(), null, pcardSubmitResponseDTO);

        }      catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred with fault code in submitPcard{}",ex.getFaultCode());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex,StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations ,faultCode, faultMessage);
        } catch (JAXBException e) {
    throw new SoapMarshallingException("Failed to marshal SOAP request object", e);
}

    }

    @Override
    public TransactionalAppResponseDTO<PaymentParamsResponseDTO> fetchPcardPaymentParams(LanguageEnum clientLanguage, PaymentParamsRequestDTO requestDTO) {

        try {

            ObjectFactory objectFactory = new ObjectFactory();

            AcceptLanguages headerObject = new AcceptLanguages();
            headerObject.setLanguage(clientLanguage.getCbtType());

            // 2. Create JAXB Marshaller
            JAXBContext jaxbContext = JAXBContext.newInstance(AcceptLanguages.class);
            Marshaller marshaller = jaxbContext.createMarshaller();

            InitiatePCardAppPaymentRequestVo initiatePCardAppPaymentRequestVo = new InitiatePCardAppPaymentRequestVo();

            initiatePCardAppPaymentRequestVo.setReferenceId(requestDTO.getApplicationReferenceNumber());
            initiatePCardAppPaymentRequestVo.setPaymentChannel(requestDTO.getPaymentMeansType().getCbtType());
            initiatePCardAppPaymentRequestVo.setBusinessEntityId(requestDTO.getBeId());
            initiatePCardAppPaymentRequestVo.setChannel(requestDTO.getRequestChannel().getCbtType());


            WebServiceMessageCallback headerCallback = getWebServiceMessageCallback(marshaller, headerObject,GET_PAYMENT_PARAMETERS_SOAP_ACTION);

            JAXBElement<InitiatePCardAppPaymentRequestVo> jaxbRequest = objectFactory.createInitiatePCardAppPaymentRequest(initiatePCardAppPaymentRequestVo);

            // 5. Send request with header
            @SuppressWarnings("unchecked")
            JAXBElement<InitiatePCardAppPaymentResponseVo> responseWrapper =
                    (JAXBElement<InitiatePCardAppPaymentResponseVo>) getTemplate().marshalSendAndReceive(
                            P_CARD_APPLICATION,
                            jaxbRequest,
                            headerCallback
                    );


            InitiatePCardAppPaymentResponseVo response = responseWrapper.getValue();

            log.info("Request for Personalised card Review : {}", requestDTO.getTransactionId());

            PaymentParamsResponseDTO paymentParamsResponseDTO = new PaymentParamsResponseDTO();

            paymentParamsResponseDTO.setApplicationReferenceNumber(requestDTO.getApplicationReferenceNumber());
           List<LineItemResponseDTO> lineItemResponseDTOS = response.getLineItems().stream().map(
                   lineItemVo -> {
                       LineItemResponseDTO lineItemResponseDTO = new LineItemResponseDTO();
                       lineItemResponseDTO.setName(lineItemVo.getName());
                       lineItemResponseDTO.setValue(lineItemVo.getValue());
                       return lineItemResponseDTO;
                   }
           ).toList();
           paymentParamsResponseDTO.setLineItems(lineItemResponseDTOS);
           paymentParamsResponseDTO.setTotalAmount(response.getTotalAmount());
           paymentParamsResponseDTO.setPaymentMeansType(PaymentMeansTypeEnum.fromCbtType(response.getPaymentChannel()));
            paymentParamsResponseDTO.setPaymentParameters(
                    response.getPaymentParameter()
                            .stream()
                            .collect(Collectors.toMap(
                                    PaymentParameterVo::getKey,
                                    PaymentParameterVo::getValue,
                                    (v1, v2) -> v2)
                            )
            );


            return TransactionalAppResponseDTO.of(requestDTO.getTransactionId(), MessageKeyEnum.SUCCESS.getCode(), MessageKeyEnum.SUCCESS.getMessage(), null, paymentParamsResponseDTO);

        }      catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred with fault code in submitPcard{}",ex.getFaultCode());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex,StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations ,faultCode, faultMessage);
        } catch (JAXBException e) {
            throw new SoapMarshallingException("Failed to marshal SOAP request object", e);
        }


    }

    @Override
    public TransactionalAppResponseDTO<TrackPcardResponseDTO> trackPcard(LanguageEnum clientLanguage, TrackPcardRequestDTO requestDTO) {

        try {
            ObjectFactory factory = new ObjectFactory();
            AcceptLanguages headerObject = new AcceptLanguages();
            headerObject.setLanguage(clientLanguage.getCbtType());

            JAXBContext jaxbContext = JAXBContext.newInstance(AcceptLanguages.class);
            Marshaller marshaller = jaxbContext.createMarshaller();

            TrackApplicationStatusRequest trackAppStatusRequest = new TrackApplicationStatusRequest();
            trackAppStatusRequest.setReferenceId(requestDTO.getApplicationReferenceNumber());

            WebServiceMessageCallback headerCallback = getWebServiceMessageCallback(marshaller, headerObject,"trackPCardApplication");

            JAXBElement<TrackApplicationStatusRequest> wrappedRequest = factory.createTrackApplicationStatusRequest(trackAppStatusRequest);

            EnumResponse trackPcardAppResponse =
                    ((JAXBElement<EnumResponse>) getTemplate().marshalSendAndReceive(
                            P_CARD_APPLICATION,
                            wrappedRequest,
                            headerCallback
                    )).getValue();
            TrackPcardResponseDTO responseDTO = new TrackPcardResponseDTO();
            responseDTO.setApplicationReferenceNumber(requestDTO.getApplicationReferenceNumber());
            responseDTO.setApplicationStatus(ApplicationStatusEnum.CBT_TYPE_TO_STATUS_MAP.get(trackPcardAppResponse.getCode()));
            return TransactionalAppResponseDTO.of(requestDTO.getTransactionId(), MessageKeyEnum.SUCCESS.getCode(), MessageKeyEnum.SUCCESS.getMessage(), null, responseDTO);

        } catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred with fault code in submitPcard{}", ex.getFaultCode());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        } catch (JAXBException e) {
            throw new SoapMarshallingException("Failed to marshal SOAP request object", e);

        }
    }
    private static WebServiceMessageCallback getWebServiceMessageCallback(Marshaller marshaller, AcceptLanguages headerObject ,String soapAction) {
        return message -> {
            SaajSoapMessage saajSoapMessage = (SaajSoapMessage) message;
            SOAPMessage soapMessage = saajSoapMessage.getSaajMessage();
            SOAPPart soapPart = soapMessage.getSOAPPart();

            SOAPEnvelope envelope;
            try {
                envelope = soapPart.getEnvelope();
            } catch (SOAPException e) {
                throw new SoapHeaderException("Error retrieving SOAP envelope", e);
            }

            SOAPHeader header;
            try {
                header = envelope.getHeader();
                if (header == null) {
                    header = envelope.addHeader();
                }
            } catch (SOAPException e) {
                throw new SoapHeaderException("Error accessing or creating SOAP header", e);
            }

            // Marshal the AcceptLanguages object into the SOAP header
            try {
                marshaller.marshal(headerObject, header);
            } catch (JAXBException e) {
                throw new SoapHeaderException("Error marshalling AcceptLanguages to SOAP header", e);
            }
            saajSoapMessage.setSoapAction(soapAction);
        };
    }



}
