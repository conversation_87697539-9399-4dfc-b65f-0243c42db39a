package com.paycraft.rta.abt.cam.common.domain.entities.tibco;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "CAM_MST_CBT_NATIONALITY")
public class CamMstCbtNationality {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "GDRFA_ID")
    private Integer gdrfaId;

    @Column(name = "GDRFA_NAME_EN")
    private String gdrfaNameEn;

    @Column(name = "GDRFA_NAME_AR")
    private String gdrfaNameAr;

    @Column(name = "GDRFA_ISO_CODE")
    private String gdrfaIsoCode;

    @Column(name = "CBT_ID")
    private String cbtId;

    @Column(name = "CBT_NAME_EN")
    private String cbtNameEn;

    @Column(name = "CBT_NAME_AR")
    private String cbtNameAr;

    private String language;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;
}
