package com.paycraft.rta.abt.cam.common.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;

@AllArgsConstructor
@Getter
public enum MessageKeyEnum {

    SUCCESS("Success",  String.valueOf(HttpStatus.OK.value())),
    FAIL("Payment Failed",  "3000"),
    VALIDATION_FAILED("Validation Failed", String.valueOf(3002)),
    RESOURCE_NOT_FOUND("Resource Not Found", String.valueOf(HttpStatus.NOT_FOUND.value())),
    A_CARD_ACTIVATION_SUCCESSFUL("Card Activated Successfully", String.valueOf(HttpStatus.OK)),
    LOYALTY_LINKED("Loyalty Link request successful", String.valueOf(HttpStatus.OK.value())),
    LOYALTY_DELINKED("Loyalty Id Delinked successfully",String.valueOf(HttpStatus.OK.value())),
    VEHICLE_LINKED("Vehicle number linked successfully",String.valueOf(HttpStatus.OK.value())),
    VEHICLE_UNLINKED("Vehicle number unlinked successfully",String.valueOf(HttpStatus.OK.value())),
    AUTO_RENEWAL_CANCEL("Auto Renewal has been successfully canceled",String.valueOf(HttpStatus.OK.value())),

    AUTO_RENEWAL_RESUME("Auto Renewal has been successfully resumed",String.valueOf(HttpStatus.OK.value())),

    AUTO_RENEWAL_SUSPEND("Auto Renewal has been successfully suspended",String.valueOf(HttpStatus.OK.value())),

    AUTO_RENEWAL_UPDATE("Auto Renewal has been successfully updated",String.valueOf(HttpStatus.OK.value())),

    AUTO_RENEWAL_SET_UP("Auto Renewal has been setup successfully",String.valueOf(HttpStatus.OK.value())),
    INVALID_REQUEST("Invalid Request", String.valueOf(HttpStatus.BAD_REQUEST.value())),
    AUTO_TOPUP_SET_UP("Auto TopUp has been setup successfully",String.valueOf(HttpStatus.OK.value())),
    AUTO_TOPUP_UPDATE("Auto TopUp has been successfully updated",String.valueOf(HttpStatus.OK.value())),
    AUTO_TOPUP_CANCEL("Auto Topup has been successfully canceled",String.valueOf(HttpStatus.OK.value())),

    AUTO_TOPUP_RESUME("Auto Topup has been successfully resumed",String.valueOf(HttpStatus.OK.value())),

    AUTO_TOPUP_SUSPEND("Auto Topup has been successfully suspended",String.valueOf(HttpStatus.OK.value())),
    RESOURCE_CONFLICT( "Resource conflict. Request already exists in the AA (Awaiting Approval) state.",String.valueOf(HttpStatus.CONFLICT.value())),
    SERVICE_CALL_FAILED("Service call to downstream system failed", String.valueOf(HttpStatus.BAD_GATEWAY.value())),
    DOWNSTREAM_SERVICE_ERROR("Downstream service returned an error response", String.valueOf(HttpStatus.BAD_GATEWAY.value())),
    UNABLE_TO_SEND("OTP could not be sent to the intended recipient.",String.valueOf(HttpStatus.UNPROCESSABLE_ENTITY.value())),
    REQUEST_TIME_OUT("External service timed out", String.valueOf(HttpStatus.REQUEST_TIMEOUT));
    private final String message;
    private final String code;
}
