package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard;

import com.paycraft.rta.abt.cam.common.domain.enums.FareMediaFormFactorEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class PcardReviewSubmitRequestDTO extends UserDetailsRequestDTO{
    private String  documentGroupReferenceId;
    private Map<String,String> attributes;
    private YesOrNoEnum identityCheckPassed;
    private YesOrNoEnum onBehalfCheckPassed;
    private YesOrNoEnum concessionCheckPassed;
    private YesOrNoEnum isicCheckPassed;
    @NotNull
    private FareMediaFormFactorEnum fareMediaFormFactor;
//    @NotBlank
//    private String mobileUserId;
    private PaymentMeansTypeEnum paymentMeansType;
    @NotBlank
    private String autoValidationReferenceNumber;
}
