package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamPcardUserApplicationRecord;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CamPcardUserAppRecordRepository extends JpaRepository<CamPcardUserApplicationRecord,Long> {

    @Query(value = "SELECT FNC_GEN_USER_APP_REF_NO FROM dual", nativeQuery = true)
    String generateUserAppRefNo();
    Optional<CamPcardUserApplicationRecord> findByAutoValidationReferenceNumber(@NotBlank String autoValidationReferenceNumber);

    @Query(value = "SELECT fnc_gen_awb_number FROM dual", nativeQuery = true)
    String generateAwbNumber();
}
