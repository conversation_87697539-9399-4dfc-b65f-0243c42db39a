package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.UserNameDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.GenderEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.SalutationEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegisterCardExHouseConfirmRequestDTO extends UserNameDetailsDTO {
    @NotBlank
    private String referenceId;
    @NotNull
    private Integer cardIteration;
    @Valid
    @NotNull
    private MobileNumberDTO mobileNumber;
    @Valid
    private MobileNumberDTO alternateMobileNumber;
    @NotBlank
    @Email
    private String email;
    @NotNull
    private GenderEnum gender;
    @NotNull
    private LocalDate dateOfBirth;
    @NotBlank
    private String emiratesId;
    @NotNull
    private YesOrNoEnum isCardPresented;

}
