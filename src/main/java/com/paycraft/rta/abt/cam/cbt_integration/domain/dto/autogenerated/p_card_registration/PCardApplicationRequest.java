//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import javax.xml.datatype.XMLGregorianCalendar;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.Address;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.ConcessionType;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.DocumentType;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.DonationAttributes;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.FullName;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.IsicInfo;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.PassengerClass;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.PhoneNumber;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.Salutation;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.UploadFileReference;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for pCardApplicationRequest complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="pCardApplicationRequest"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="dateOfBirth" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/&gt;
 *         &lt;element name="authConcessionExpireDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/&gt;
 *         &lt;element name="address" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}address" minOccurs="0"/&gt;
 *         &lt;element name="airwayBillNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="alternateMobileNumber" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}phoneNumber" minOccurs="0"/&gt;
 *         &lt;element name="arabicName" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}fullName" minOccurs="0"/&gt;
 *         &lt;element name="attributes" type="{http://www.rta.ae/ActiveMatrix/ESB/DonationPaymentService/XMLSchema}donationAttributes" minOccurs="0"/&gt;
 *         &lt;element name="cardMedia" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="concessionDocument" type="{http://www.rta.ae/ActiveMatrix/ESB/UploadService/XMLSchema}uploadFileReference" minOccurs="0"/&gt;
 *         &lt;element name="concessionType" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}concessionType" minOccurs="0"/&gt;
 *         &lt;element name="documentId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="documentType" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}documentType" minOccurs="0"/&gt;
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="emiratesID" type="{http://www.rta.ae/ActiveMatrix/ESB/UploadService/XMLSchema}uploadFileReference" minOccurs="0"/&gt;
 *         &lt;element name="guardianDocument" type="{http://www.rta.ae/ActiveMatrix/ESB/UploadService/XMLSchema}uploadFileReference" minOccurs="0"/&gt;
 *         &lt;element name="identityCheckPassed" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="isicEnabled" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="isicInfo" type="{http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/2_0}isicInfo" minOccurs="0"/&gt;
 *         &lt;element name="layoutId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="mobileNumber" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}phoneNumber" minOccurs="0"/&gt;
 *         &lt;element name="mobileUserId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="name" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}fullName" minOccurs="0"/&gt;
 *         &lt;element name="nationalityId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="onBehalfCheckPassed" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="passengerClass" type="{http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema}passengerClass" minOccurs="0"/&gt;
 *         &lt;element name="paymentChannel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="photo" type="{http://www.rta.ae/ActiveMatrix/ESB/UploadService/XMLSchema}uploadFileReference" minOccurs="0"/&gt;
 *         &lt;element name="requestChannel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="salutation" type="{http://www.rta.ae/ActiveMatrix/ESB/Personal/XMLSchema}salutation" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pCardApplicationRequest", propOrder = {
    "dateOfBirth",
    "authConcessionExpireDate",
    "address",
    "airwayBillNumber",
    "alternateMobileNumber",
    "arabicName",
    "attributes",
    "cardMedia",
    "concessionDocument",
    "concessionType",
    "documentId",
    "documentType",
    "email",
    "emiratesID",
    "guardianDocument",
    "identityCheckPassed",
    "isicEnabled",
    "isicInfo",
    "layoutId",
    "mobileNumber",
    "mobileUserId",
    "name",
    "nationalityId",
    "onBehalfCheckPassed",
    "passengerClass",
    "paymentChannel",
    "photo",
    "requestChannel",
    "salutation"
})
public class PCardApplicationRequest {

    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar dateOfBirth;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar authConcessionExpireDate;
protected Address address;
    protected String airwayBillNumber;
protected PhoneNumber alternateMobileNumber;
protected FullName arabicName;
protected DonationAttributes attributes;
    protected String cardMedia;
protected UploadFileReference concessionDocument;
    @XmlSchemaType(name = "string")
protected ConcessionType concessionType;
    protected String documentId;
    @XmlSchemaType(name = "string")
protected DocumentType documentType;
    protected String email;
protected UploadFileReference emiratesID;
protected UploadFileReference guardianDocument;
    protected String identityCheckPassed;
    protected int isicEnabled;
protected IsicInfo isicInfo;
    protected Integer layoutId;
protected PhoneNumber mobileNumber;
    protected String mobileUserId;
protected FullName name;
    protected Integer nationalityId;
    protected String onBehalfCheckPassed;
    @XmlSchemaType(name = "string")
protected PassengerClass passengerClass;
    protected String paymentChannel;
protected UploadFileReference photo;
    protected String requestChannel;
    @XmlSchemaType(name = "string")
protected Salutation salutation;

    /**
     * Gets the value of the dateOfBirth property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfBirth() {
        return dateOfBirth;
    }

    /**
     * Sets the value of the dateOfBirth property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDateOfBirth(XMLGregorianCalendar value) {
        this.dateOfBirth = value;
    }

    /**
     * Gets the value of the authConcessionExpireDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAuthConcessionExpireDate() {
        return authConcessionExpireDate;
    }

    /**
     * Sets the value of the authConcessionExpireDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAuthConcessionExpireDate(XMLGregorianCalendar value) {
        this.authConcessionExpireDate = value;
    }

    /**
     * Gets the value of the address property.
     * 
     * @return
     *     possible object is
     *     {@link Address }
     *     
     */
public Address getAddress() {
        return address;
    }

    /**
     * Sets the value of the address property.
     * 
     * @param value
     *     allowed object is
     *     {@link Address }
     *     
     */
    public void setAddress(Address value) {
        this.address = value;
    }

    /**
     * Gets the value of the airwayBillNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAirwayBillNumber() {
        return airwayBillNumber;
    }

    /**
     * Sets the value of the airwayBillNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAirwayBillNumber(String value) {
        this.airwayBillNumber = value;
    }

    /**
     * Gets the value of the alternateMobileNumber property.
     * 
     * @return
     *     possible object is
     *     {@link PhoneNumber }
     *     
     */
public PhoneNumber getAlternateMobileNumber() {
        return alternateMobileNumber;
    }

    /**
     * Sets the value of the alternateMobileNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link PhoneNumber }
     *     
     */
    public void setAlternateMobileNumber(PhoneNumber value) {
        this.alternateMobileNumber = value;
    }

    /**
     * Gets the value of the arabicName property.
     * 
     * @return
     *     possible object is
     *     {@link FullName }
     *     
     */
public FullName getArabicName() {
        return arabicName;
    }

    /**
     * Sets the value of the arabicName property.
     * 
     * @param value
     *     allowed object is
     *     {@link FullName }
     *     
     */
    public void setArabicName(FullName value) {
        this.arabicName = value;
    }

    /**
     * Gets the value of the attributes property.
     * 
     * @return
     *     possible object is
     *     {@link DonationAttributes }
     *     
     */
public DonationAttributes getAttributes() {
        return attributes;
    }

    /**
     * Sets the value of the attributes property.
     * 
     * @param value
     *     allowed object is
     *     {@link DonationAttributes }
     *     
     */
    public void setAttributes(DonationAttributes value) {
        this.attributes = value;
    }

    /**
     * Gets the value of the cardMedia property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCardMedia() {
        return cardMedia;
    }

    /**
     * Sets the value of the cardMedia property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCardMedia(String value) {
        this.cardMedia = value;
    }

    /**
     * Gets the value of the concessionDocument property.
     * 
     * @return
     *     possible object is
     *     {@link UploadFileReference }
     *     
     */
public UploadFileReference getConcessionDocument() {
        return concessionDocument;
    }

    /**
     * Sets the value of the concessionDocument property.
     * 
     * @param value
     *     allowed object is
     *     {@link UploadFileReference }
     *     
     */
    public void setConcessionDocument(UploadFileReference value) {
        this.concessionDocument = value;
    }

    /**
     * Gets the value of the concessionType property.
     * 
     * @return
     *     possible object is
     *     {@link ConcessionType }
     *     
     */
public ConcessionType getConcessionType() {
        return concessionType;
    }

    /**
     * Sets the value of the concessionType property.
     * 
     * @param value
     *     allowed object is
     *     {@link ConcessionType }
     *     
     */
    public void setConcessionType(ConcessionType value) {
        this.concessionType = value;
    }

    /**
     * Gets the value of the documentId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocumentId() {
        return documentId;
    }

    /**
     * Sets the value of the documentId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocumentId(String value) {
        this.documentId = value;
    }

    /**
     * Gets the value of the documentType property.
     * 
     * @return
     *     possible object is
     *     {@link DocumentType }
     *     
     */
public DocumentType getDocumentType() {
        return documentType;
    }

    /**
     * Sets the value of the documentType property.
     * 
     * @param value
     *     allowed object is
     *     {@link DocumentType }
     *     
     */
    public void setDocumentType(DocumentType value) {
        this.documentType = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the emiratesID property.
     * 
     * @return
     *     possible object is
     *     {@link UploadFileReference }
     *     
     */
public UploadFileReference getEmiratesID() {
        return emiratesID;
    }

    /**
     * Sets the value of the emiratesID property.
     * 
     * @param value
     *     allowed object is
     *     {@link UploadFileReference }
     *     
     */
    public void setEmiratesID(UploadFileReference value) {
        this.emiratesID = value;
    }

    /**
     * Gets the value of the guardianDocument property.
     * 
     * @return
     *     possible object is
     *     {@link UploadFileReference }
     *     
     */
public UploadFileReference getGuardianDocument() {
        return guardianDocument;
    }

    /**
     * Sets the value of the guardianDocument property.
     * 
     * @param value
     *     allowed object is
     *     {@link UploadFileReference }
     *     
     */
    public void setGuardianDocument(UploadFileReference value) {
        this.guardianDocument = value;
    }

    /**
     * Gets the value of the identityCheckPassed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdentityCheckPassed() {
        return identityCheckPassed;
    }

    /**
     * Sets the value of the identityCheckPassed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdentityCheckPassed(String value) {
        this.identityCheckPassed = value;
    }

    /**
     * Gets the value of the isicEnabled property.
     * 
     */
    public int getIsicEnabled() {
        return isicEnabled;
    }

    /**
     * Sets the value of the isicEnabled property.
     * 
     */
    public void setIsicEnabled(int value) {
        this.isicEnabled = value;
    }

    /**
     * Gets the value of the isicInfo property.
     * 
     * @return
     *     possible object is
     *     {@link IsicInfo }
     *     
     */
public IsicInfo getIsicInfo() {
        return isicInfo;
    }

    /**
     * Sets the value of the isicInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link IsicInfo }
     *     
     */
    public void setIsicInfo(IsicInfo value) {
        this.isicInfo = value;
    }

    /**
     * Gets the value of the layoutId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLayoutId() {
        return layoutId;
    }

    /**
     * Sets the value of the layoutId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLayoutId(Integer value) {
        this.layoutId = value;
    }

    /**
     * Gets the value of the mobileNumber property.
     * 
     * @return
     *     possible object is
     *     {@link PhoneNumber }
     *     
     */
public PhoneNumber getMobileNumber() {
        return mobileNumber;
    }

    /**
     * Sets the value of the mobileNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link PhoneNumber }
     *     
     */
    public void setMobileNumber(PhoneNumber value) {
        this.mobileNumber = value;
    }

    /**
     * Gets the value of the mobileUserId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMobileUserId() {
        return mobileUserId;
    }

    /**
     * Sets the value of the mobileUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobileUserId(String value) {
        this.mobileUserId = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link FullName }
     *     
     */
public FullName getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link FullName }
     *     
     */
    public void setName(FullName value) {
        this.name = value;
    }

    /**
     * Gets the value of the nationalityId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNationalityId() {
        return nationalityId;
    }

    /**
     * Sets the value of the nationalityId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNationalityId(Integer value) {
        this.nationalityId = value;
    }

    /**
     * Gets the value of the onBehalfCheckPassed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOnBehalfCheckPassed() {
        return onBehalfCheckPassed;
    }

    /**
     * Sets the value of the onBehalfCheckPassed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOnBehalfCheckPassed(String value) {
        this.onBehalfCheckPassed = value;
    }

    /**
     * Gets the value of the passengerClass property.
     * 
     * @return
     *     possible object is
     *     {@link PassengerClass }
     *     
     */
public PassengerClass getPassengerClass() {
        return passengerClass;
    }

    /**
     * Sets the value of the passengerClass property.
     * 
     * @param value
     *     allowed object is
     *     {@link PassengerClass }
     *     
     */
    public void setPassengerClass(PassengerClass value) {
        this.passengerClass = value;
    }

    /**
     * Gets the value of the paymentChannel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPaymentChannel() {
        return paymentChannel;
    }

    /**
     * Sets the value of the paymentChannel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaymentChannel(String value) {
        this.paymentChannel = value;
    }

    /**
     * Gets the value of the photo property.
     * 
     * @return
     *     possible object is
     *     {@link UploadFileReference }
     *     
     */
public UploadFileReference getPhoto() {
        return photo;
    }

    /**
     * Sets the value of the photo property.
     * 
     * @param value
     *     allowed object is
     *     {@link UploadFileReference }
     *     
     */
    public void setPhoto(UploadFileReference value) {
        this.photo = value;
    }

    /**
     * Gets the value of the requestChannel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestChannel() {
        return requestChannel;
    }

    /**
     * Sets the value of the requestChannel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestChannel(String value) {
        this.requestChannel = value;
    }

    /**
     * Gets the value of the salutation property.
     * 
     * @return
     *     possible object is
     *     {@link Salutation }
     *     
     */
public Salutation getSalutation() {
        return salutation;
    }

    /**
     * Sets the value of the salutation property.
     * 
     * @param value
     *     allowed object is
     *     {@link Salutation }
     *     
     */
    public void setSalutation(Salutation value) {
        this.salutation = value;
    }

}
