package com.paycraft.rta.abt.cam.common.domain.entities.tibco;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "CAM_PCARD_USER_APPLICATION_RECORD")
public class CamPcardUserApplicationRecord {

    @Id
    @SequenceGenerator(name = "userAppRecordSeq", sequenceName = "USER_APP_RECORD_SEQ", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "userAppRecordSeq")
    @Column(name = "ID", nullable = false)
    private Long id;

    // Request data
    @Column(name = "BE_ID")
    private Integer beId;

    @Column(name = "REQUEST_CHANNEL", length = 50)
    private String requestChannel;

    @Column(name = "TRANSACTION_ID", length = 100)
    private String transactionId;

    @Column(name = "ON_BEHALF_TRANSACTION_ID", length = 100)
    private String onBehalfTransactionId;

    @Column(name = "IAM_USER_ID", length = 100)
    private String iamUserId;

    @Column(name = "UAE_PASS_USER_ID", length = 100)
    private String uaePassUserId;

    @Column(name = "ABT_ACCOUNT_ID", length = 100)
    private String abtAccountId;

    @Column(name = "GUEST_EMAIL", length = 150)
    private String guestEmail;

    @Column(name = "GUEST_MOBILE_COUNTRY_CODE", length = 3)
    private String guestMobileCountryCode;

    @Column(name = "GUEST_MOBILE_AREA_CODE", length = 2)
    private String guestMobileAreaCode;

    @Column(name = "GUEST_MOBILE_NUMBER", length = 7)
    private String guestMobileNumber;

    @Column(name = "NAME_EN", length = 100)
    private String nameEn;

    @Column(name = "NAME_AR", length = 100)
    private String nameAr;

    @Column(name = "FIRST_NAME_EN", length = 100)
    private String firstNameEn;

    @Column(name = "FIRST_NAME_AR", length = 100)
    private String firstNameAr;

    @Column(name = "LAST_NAME_EN", length = 100)
    private String lastNameEn;

    @Column(name = "LAST_NAME_AR", length = 100)
    private String lastNameAr;

    @Column(name = "SECOND_NAME_EN", length = 100)
    private String secondNameEn;

    @Column(name = "SECOND_NAME_AR", length = 100)
    private String secondNameAr;

    @Column(name = "MOBILE_COUNTRY_CODE", length = 3)
    private String mobileCountryCode;

    @Column(name = "MOBILE_AREA_CODE", length = 2)
    private String mobileAreaCode;

    @Column(name = "MOBILE_NUMBER", length = 7)
    private String mobileNumber;

    @Column(name = "ALT_MOBILE_COUNTRY_CODE", length = 3)
    private String altMobileCountryCode;

    @Column(name = "ALT_MOBILE_AREA_CODE", length = 2)
    private String altMobileAreaCode;

    @Column(name = "ALT_MOBILE_NUMBER", length = 7)
    private String altMobileNumber;

    @Column(name = "EMAIL", length = 150)
    private String email;

    @Column(name = "ADDRESS_EMIRATE", length = 50)
    private String addressEmirate;

    @Column(name = "ADDRESS_CITY", length = 50)
    private String addressCity;

    @Column(name = "ADDRESS_STREET", length = 100)
    private String addressStreet;

    @Column(name = "ADDRESS_HOUSE_NO", length = 50)
    private String addressHouseNo;

    @Column(name = "PHOTO_REFERENCE_ID", length = 100)
    private String photoReferenceId;

    @Column(name = "SERVICE_CLASS")
    private Integer serviceClass;

    @Column(name = "ART_WORK_ID")
    private Integer artWorkId;

    @Column(name = "CONCESSION_TYPE", length = 50)
    private String concessionType;

    @Column(name = "NATIONALITY_ID")
    private Integer nationalityId;

    @Column(name = "MOBILE_USER_ID", length = 100)
    private String mobileUserId;

    @Column(name = "DATE_OF_BIRTH")
    private LocalDate dateOfBirth;

    @Column(name = "EMIRATES_ID")
    private String emiratesId;

    @Column(name = "PASSPORT_NO", length = 50)
    private String passportNo;

    @Column(name = "ON_BEHALF_FLAG", length = 1)
    private String onBehalfFlag;

    @Column(name = "ISIC_ENABLED", length = 1)
    private String isicEnabled;

    @Column(name = "ISIC_SCHOOL_SHORT_NAME", length = 100)
    private String isicSchoolShortName;

    @Column(name = "ON_BEHALF_EMIRATES_ID")
    private Long onBehalfEmiratesId;

    @Column(name = "ON_BEHALF_DOB")
    private LocalDate onBehalfDob;

    // Response data
    @Column(name = "IDENTITY_CHECK_PASSED", length = 1)
    private String identityCheckPassed;

    @Column(name = "CONCESSION_CHECK_PASSED", length = 1)
    private String concessionCheckPassed;

    @Column(name = "KHDA_STATUS", length = 10)
    private String khdaStatus;

    @Column(name = "KHDA_ACADEMIC_YEAR", length = 50)
    private String khdaAcademicYear;

    @Column(name = "KHDA_SCHOOL_NAME", length = 100)
    private String khdaSchoolName;

    @Column(name = "SANAD_CON_NUM", length = 50)
    private String sanadConNum;

    @Column(name = "SANAD_DISABILITY_CATEGORY", length = 100)
    private String sanadDisabilityCategory;

    @Column(name = "SANAD_DISABILITY_TYPE", length = 100)
    private String sanadDisabilityType;

    @Column(name = "SANAD_MOCD_POD_NUMBER", length = 100)
    private String sanadMocdPodNumber;

    @Column(name = "SANAD_NATIONAL_SOCIAL_CUSTOMER", length = 100)
    private String sanadNationalSocialCustomer;

    @Column(name = "SANAD_POD_NUMBER", length = 100)
    private String sanadPodNumber;

    @Column(name = "SANAD_SPC_CARD", length = 100)
    private String sanadSpcCard;

    @Column(name = "SANAD_AUTH_CONCESSION_EXPIRY")
    private LocalDate sanadAuthConcessionExpiry;

    @Column(name = "ISIC_CHECK_PASSED", length = 10)
    private String isicCheckPassed;

    @Column(name = "ISIC_SERIAL_NUMBER", length = 100)
    private String isicSerialNumber;

    @Column(name = "ISIC_CARDHOLDER_ID")
    private Integer isicCardholderId;

    @Column(name = "ISIC_CARD_ID", length = 100)
    private String isicCardId;

    @Column(name = "ON_BEHALF_CHECK_PASSED", length = 1)
    private String onBehalfCheckPassed;

    @Column(name = "BEHALF_GENDER", length = 10)
    private String behalfGender;

    @Column(name = "BEHALF_NATIONALITY_ID", length = 50)
    private String behalfNationalityId;

    @Column(name = "BEHALF_NATIONALITY_NAME_EN", length = 100)
    private String behalfNationalityNameEn;

    @Column(name = "BEHALF_NATIONALITY_NAME_AR", length = 100)
    private String behalfNationalityNameAr;

    @Column(name = "BEHALF_NATIONALITY_ISO3_CODE", length = 10)
    private String behalfNationalityIso3Code;

    @Column(name = "BEHALF_RESIDENT_ID", length = 50)
    private String behalfResidentId;

    @Column(name = "BEHALF_RESIDENT_ISSUE_DATE")
    private LocalDate behalfResidentIssueDate;

    @Column(name = "BEHALF_RESIDENT_EXPIRY_DATE")
    private LocalDate behalfResidentExpiryDate;

    @Column(name = "AUTO_VALIDATION_REFERENCE_NUMBER", length = 100)
    private String autoValidationReferenceNumber;

    // Getters and Setters omitted for brevity
}

