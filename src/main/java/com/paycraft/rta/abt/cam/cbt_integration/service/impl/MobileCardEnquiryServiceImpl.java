package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.domain.mapper.*;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardKeyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.CardInfoRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.CardInfoResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.RegisteredCardListResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.GeneralHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.TravelHistoryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.TravelHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.CamCardKeyGenDetails;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.repository.CamCardKeyGenDetailsRepository;
import com.paycraft.rta.abt.cam.common.utils.DateTimeUtils;
import jakarta.validation.Valid;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.Unmarshaller;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.oxm.Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.dom.DOMSource;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.MOBILE_CARD_ENQUIRY_SERVICE;

@Service
@RequiredArgsConstructor
public class MobileCardEnquiryServiceImpl implements MobileCardEnquiryService {

    private  static final  Logger log = LogManager.getLogger(MobileCardEnquiryServiceImpl.class);

    private final SoapClientRegistry soapClientRegistry;

    @Autowired
    CamCardKeyGenDetailsRepository camCardKeyGenDetailsRepository;
    @Autowired
    CardTravelHistoryMapper travelHistoryMapper;

    public static final String SERVICE_KEY = "nolCardEnquiry";

    public CardKeyDTO findCardKey(String tagId , String userId, LanguageEnum languageClient) {

        //if present in db return it else hit cbt to get
        Optional<CamCardKeyGenDetails> optCardKeyGenDetails = camCardKeyGenDetailsRepository.findById(tagId);
        if(optCardKeyGenDetails.isPresent()){
            CamCardKeyGenDetails cardKeyGenDetails = optCardKeyGenDetails.get();
            log.info("Card key Details found in DB : {}", cardKeyGenDetails);
            return new CardKeyDTO(Integer.parseInt(cardKeyGenDetails.getCardGenNumber()),Long.parseLong(cardKeyGenDetails.getCardId()));
        }

        FindCardKeyRequest request = new FindCardKeyRequest();
        request.setTagId(tagId);

        ObjectFactory factory = new ObjectFactory();
        JAXBElement<FindCardKeyRequest> wrappedRequest = factory.createFindCardKeyRequest(request);

        CustomHeader header =factory.createCustomHeader();
        header.setLanguage(languageClient.getCbtType());
        header.setUserId(userId);

        WebServiceMessageCallback callback = buildCallbackWithHeader("findCardKey",header);// findCardKey mentioned in wsdl line no 326

        CardKey cardKeyResponse;
        CardKeyRequest cardKeyRequestResponse;
        CardKey finalCardKey = null;

        try {
            Object response = getTemplate().marshalSendAndReceive(
                    MOBILE_CARD_ENQUIRY_SERVICE,
                    wrappedRequest,
                    callback
            );
//            String xmlres = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
//                    "<SOAP-ENV:Header/>\n" +
//                    "<S:Body xmlns:ns0=\"http://www.rta.ae/ActiveMatrix/ESB/NolCardEnquiryService/XMLSchema\" xmlns:ns2=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
//                    "<ns0:cardKey>\n" +
//                    "<cardGenNumber>0</cardGenNumber>\n" +
//                    "<cardId>18297000900000000</cardId>\n" +
//                    "</ns0:cardKey>\n" +
//                    "</S:Body>\n" +
//                    "</S:Envelope>";
//            Object response = parseStaticSoapResponse(xmlres);

            log.info("=== DEBUG RESPONSE ===");
            log.info("Response class: " + response.getClass().getName());

            if (response instanceof JAXBElement) {
                JAXBElement<?> jaxbElement = (JAXBElement<?>) response;
                log.info("JAXBElement name: " + jaxbElement.getName());
                log.info("JAXBElement value class: " + jaxbElement.getValue().getClass().getName());
                log.info("JAXBElement declared type: " + jaxbElement.getDeclaredType().getName());

                Object value = jaxbElement.getValue();

                if (value instanceof CardKey) {
                    finalCardKey = (CardKey) value;
                    log.info("SUCCESS: Got CardKey directly");
                } else if (value instanceof CardKeyRequest) {
                    log.info("Got CardKeyRequest - converting to CardKey");
                    CardKeyRequest cardKeyRequest = (CardKeyRequest) value;
                    // Convert CardKeyRequest to CardKey
                    finalCardKey = convertToCardKey(cardKeyRequest);

                } else {
                    log.info("UNKNOWN: Got " + value.getClass().getName());
                    throw new RuntimeException("Unexpected response type: " + value.getClass().getName());
                }
            } else {
                throw new RuntimeException("Response is not a JAXBElement");
            }
            if (finalCardKey.getCardGenNumber() == null || finalCardKey.getCardId() == null) {
                throw new CbtIntegrationException(null,null , MessageKeyEnum.SERVICE_CALL_FAILED.getCode(), "CBT returned malformed cardKey");
            }
        }
        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","FindCardKey");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

        CamCardKeyGenDetails newCardKeyGenDetails = new CamCardKeyGenDetails();
        newCardKeyGenDetails.setTagId(tagId);
        newCardKeyGenDetails.setCardId(finalCardKey.getCardId().toString());
        newCardKeyGenDetails.setCardGenNumber(finalCardKey.getCardGenNumber().toString());
        camCardKeyGenDetailsRepository.save(newCardKeyGenDetails);
        log.info( "CardKey from CBT: {}", finalCardKey);
        return CardKeyMapper.toDto(finalCardKey);

    }

    private JAXBElement<CardKeyRequest> parseStaticSoapResponse(String soapXml) {
        try {
            // Parse XML
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(soapXml.getBytes(StandardCharsets.UTF_8)));

            // Find the <cardKey> element
            NodeList nodeList = doc.getElementsByTagNameNS(
                    "http://www.rta.ae/ActiveMatrix/ESB/NolCardEnquiryService/XMLSchema",
                    "cardKey"
            );
            if (nodeList.getLength() == 0) {
                throw new RuntimeException("cardKey element not found in SOAP response");
            }

            Node cardKeyNode = nodeList.item(0);
            // Unmarshal it to JAXBElement<CardKey>
            JAXBContext jaxbContext = JAXBContext.newInstance(CardKeyRequest.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

            JAXBElement<CardKeyRequest> jaxbElement = unmarshaller.unmarshal(
                    new DOMSource(cardKeyNode),
                    CardKeyRequest.class
            );

            return jaxbElement;

        } catch (Exception e) {
            throw new RuntimeException("Failed to parse static SOAP response", e);
        }
    }

    private CardKey convertToCardKey(CardKeyRequest cardKeyRequest) {
        CardKey cardKey = new CardKey();
        // Example mappings (adjust based on your actual classes):
        if (cardKeyRequest.getCardId() != null) {
            cardKey.setCardId(cardKeyRequest.getCardId());
        }
        if (cardKeyRequest.getCardGenNumber() != null) {
            cardKey.setCardGenNumber(cardKeyRequest.getCardGenNumber());
        }
        return cardKey;
    }

    public List<RegisteredCardListResponseDTO> getCardsList(LanguageEnum clientLanguage, RequestDTO abtRequestDTO) {

        // 1. Build request – this operation has no input body
        ObjectFactory factory = new ObjectFactory();

        // 2. Build SOAP header
        CustomHeader header = factory.createCustomHeader();
        header.setLanguage(clientLanguage.getCbtType());
        header.setUserId(abtRequestDTO.getIamUserId());

        // 3. Build callback with SOAP action
        WebServiceMessageCallback callback = buildCallbackWithHeader("getRegisteredCards", header);

        JAXBElement<GetRegisteredCards> wrappedRequest = factory.createGetRegisteredCards(null);
        // 4. Send SOAP request and receive response
        JAXBElement<GetRegisteredCardsResponse> response = null;
        try {
            response = (JAXBElement<GetRegisteredCardsResponse>)
                    getTemplate().marshalSendAndReceive(
                            MOBILE_CARD_ENQUIRY_SERVICE,
                            wrappedRequest,
                            callback
                    );
        }
        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","getCardList");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

        // 5. Extract actual payload
        return RegisteredCardsMapper.mapToDTO(response.getValue());
    }

    public TravelHistoryResponseDTO fetchTravelHistory(LanguageEnum clientLanguage, @Valid TravelHistoryRequestDTO requestDTO) {

        // 1. Build the CardKey
        CardKeyDTO cardKeyResponse = findCardKey(requestDTO.getNolCardId(),requestDTO.getIamUserId(),clientLanguage);
        CardKey cardKey = new CardKey();
        cardKey.setCardGenNumber(cardKeyResponse.getCardGenNumber());
        cardKey.setCardId(cardKeyResponse.getCardId());

        // 2. Create Request Object
        CardHistoryRequest request = new CardHistoryRequest();
//        request.getCardKey().setCardId(cardKey.getCardId());
//        request.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
        request.setCardKey(cardKey);
        log.info("#########################   Travel History : converting local time to UTC time.....    #########################");
        OffsetDateTime dateFrom = requestDTO.getDateFrom().atZone(ZoneId.of("Asia/Dubai")).toOffsetDateTime();
        OffsetDateTime dateTo = requestDTO.getDateTo().atZone(ZoneId.of("Asia/Dubai")).toOffsetDateTime();
        log.info("#########################   Travel History : Done converting local time to UTC time!    #########################");
        log.info("#########################    DateFrom : {}    #########################", requestDTO.getDateFrom());
        log.info("#########################    DateTo : {}    #########################", requestDTO.getDateTo());
        request.setDateFrom(convertToXmlGregorianCalendar(dateFrom));
        request.setDateTo(convertToXmlGregorianCalendar(dateTo));
        request.setHistoryType(HistoryType.valueOf(requestDTO.getHistoryType().name()));

        // 3. Wrap with JAXBElement
        ObjectFactory factory = new ObjectFactory();
        JAXBElement<CardHistoryRequest> wrappedRequest = factory.createCardTravelHistoryRequest(request);

        // 4. Create SOAP Header
        CustomHeader header = factory.createCustomHeader();
        header.setLanguage(clientLanguage.getCbtType());
        header.setUserId(requestDTO.getIamUserId());

        // 5. Build Callback
        WebServiceMessageCallback callback = buildCallbackWithHeader("getCardTravelHistory", header);

        // 6. Send request and return response
//        JAXBElement<List<CardTravelHistoryInfo>> response = null;
        JAXBElement<CardTravelHistory> response = null;
        try {
//            response = (JAXBElement<List<CardTravelHistoryInfo>>)
//                    getTemplate().marshalSendAndReceive(
//                            MOBILE_CARD_ENQUIRY_SERVICE,
//                            wrappedRequest,
//                            callback
//                    );
            response =
                    (JAXBElement<CardTravelHistory>) getTemplate().marshalSendAndReceive(
                            MOBILE_CARD_ENQUIRY_SERVICE, wrappedRequest, callback
                    );

//            CardTravelHistory cardTravelHistory = response.getValue();
//            List<AbstractCardHistoryInfo> cardHistoryList = cardTravelHistory.getHistoryItems();
        } catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","TravelHistory");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

        CardTravelHistory cardHistoryList = response.getValue(); // from JAXB
           log.info("#########################   Map to ResponseDTO for Travel History in CardTravelHistoryMapper class   ##########################");
        return  travelHistoryMapper.toDto(cardHistoryList);
    }

    public GeneralHistoryResponseDTO fetchGeneralHistory(LanguageEnum clientLanguage, TravelHistoryRequestDTO requestDTO) {

        // 1. Build the CardKey
        CardKeyDTO cardKeyResponse = findCardKey(requestDTO.getNolCardId(),requestDTO.getIamUserId(),clientLanguage);

        CardKey cardKey = new CardKey();
        cardKey.setCardGenNumber(cardKeyResponse.getCardGenNumber());
        cardKey.setCardId(cardKeyResponse.getCardId());

        // 2. Create Request Object
        CardHistoryRequest request = new CardHistoryRequest();
//        request.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
//        request.getCardKey().setCardId(cardKey.getCardId());
        request.setCardKey(cardKey);
        log.info("#########################    General History API: converting local time to UTC time.....    #########################");
        OffsetDateTime dateFrom = requestDTO.getDateFrom().atZone(ZoneId.of("Asia/Dubai")).toOffsetDateTime();
        OffsetDateTime dateTo = requestDTO.getDateTo().atZone(ZoneId.of("Asia/Dubai")).toOffsetDateTime();
        log.info("#########################    General History API: Done converting local time to UTC time!    #########################");
        log.info("#########################    DateFrom : {}    #########################", requestDTO.getDateFrom());
        log.info("#########################    DateTo : {}    #########################", requestDTO.getDateTo());
        request.setDateFrom(convertToXmlGregorianCalendar(dateFrom));
        request.setDateTo(convertToXmlGregorianCalendar(dateTo));
        request.setHistoryType(HistoryType.valueOf(requestDTO.getHistoryType().name()));
        request.setNumberOfRecords(requestDTO.getNoOfRecord());

        // 1. Wrap request using JAXB ObjectFactory
        ObjectFactory factory = new ObjectFactory();
        JAXBElement<CardHistoryRequest> wrappedRequest = factory.createCardGeneralHistoryRequest(request);

        // Create custom header
        CustomHeader header = new ObjectFactory().createCustomHeader();
        header.setLanguage(clientLanguage.getCbtType());
        header.setUserId(requestDTO.getIamUserId());

        // 2. Build SOAP callback (header + action)
        WebServiceMessageCallback callback = buildCallbackWithHeader("getCardGeneralHistory",header);

        // 3. Send request to endpoint and receive response
        JAXBElement<CardGeneralHistory> response = null;
        try {
            response = (JAXBElement<CardGeneralHistory>)
                    getTemplate().marshalSendAndReceive(
                            MOBILE_CARD_ENQUIRY_SERVICE,
                            wrappedRequest,
                            callback
                    );
        }

        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","generalHistory");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
        return CardGeneralHistoryMapper.toDto(response.getValue());
    }

    public CardInfoResponseDTO getCardInfo(LanguageEnum clientLanguage,CardInfoRequestDTO requestDTO) {

        CardKeyDTO cardKey = findCardKey(requestDTO.getNolCardId(),requestDTO.getIamUserId(),clientLanguage);
        // 1. Build the CardInfo object
        CardKeyRequest cardInfo = new CardKeyRequest();
        cardInfo.setCardId(cardKey.getCardId());
        cardInfo.setCardGenNumber(cardKey.getCardGenNumber());
        cardInfo.setPin(requestDTO.getPin());
        Optional.ofNullable(requestDTO.getAdvanceMode()).ifPresent(advanceMode -> cardInfo.setAdvanceMode(advanceMode.getId()));

        ObjectFactory factory = new ObjectFactory();
        JAXBElement<CardKeyRequest> wrappedRequest = factory.createCardKey(cardInfo);

        // 3. Prepare SOAP Header
        CustomHeader header = factory.createCustomHeader();
        header.setLanguage(clientLanguage.getCbtType());
        header.setUserId(requestDTO.getIamUserId());

        // 4. Build SOAP Callback
        WebServiceMessageCallback callback = buildCallbackWithHeader("getCardInfo", header);
        JAXBElement<CardInfoAdvanceResponse> response;

        try{
            // 5. Send SOAP request and receive response
            response = (JAXBElement<CardInfoAdvanceResponse>)
                    getTemplate().marshalSendAndReceive(
                            MOBILE_CARD_ENQUIRY_SERVICE,
                            wrappedRequest,
                            callback
                    );
        }

        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","card-info");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
        CardInfoResponseDTO responseDTO= CardInfoMapper.toDTO(response.getValue());
        responseDTO.setNolCardId(responseDTO.getNolCardId());
        return responseDTO;
    }

    private WebServiceMessageCallback buildCallbackWithHeader(String actionName , CustomHeader header) {
        //SoapActionCallback actionCallback = new SoapActionCallback(MOBILE_CARD_ENQUIRY_SERVICE + actionName );
        SoapActionCallback actionCallback = new SoapActionCallback(actionName);// only adding soapAction
        Marshaller marshaller = soapClientRegistry.getTemplate(SERVICE_KEY).getMarshaller(); // Spring Marshaller

        return message -> {
            SoapMessage soapMessage = (SoapMessage) message;
            // Directly marshal header into SOAP header (no JAXBElement)
            marshaller.marshal(header, soapMessage.getSoapHeader().getResult());
            // Apply SOAP Action
            actionCallback.doWithMessage(message);
        };
    }

    private XMLGregorianCalendar toXmlDate(LocalDateTime dateTime) {
        try {
            GregorianCalendar cal = GregorianCalendar.from(dateTime.atZone(ZoneId.systemDefault()));
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        } catch (DatatypeConfigurationException e) {
            throw new RuntimeException("Date conversion failed", e);
        }
    }

    public XMLGregorianCalendar convertToXmlGregorianCalendar(OffsetDateTime offsetDateTime) {
        GregorianCalendar calendar = GregorianCalendar.from(offsetDateTime.toZonedDateTime());
        try {
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(calendar);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert OffsetDateTime to XMLGregorianCalendar", e);
        }
    }

    private WebServiceTemplate getTemplate() {
        return soapClientRegistry.getTemplate(SERVICE_KEY);
    }
}
