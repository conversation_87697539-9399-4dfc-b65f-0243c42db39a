package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtConcessionType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CamMstCbtConcessionTypeRepository extends JpaRepository<CamMstCbtConcessionType, String> {
    List<CamMstCbtConcessionType> findByLanguage(String languageCode);
}
