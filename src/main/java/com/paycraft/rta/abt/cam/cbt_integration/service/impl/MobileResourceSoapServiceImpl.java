package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.MobileResourceSoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_resources.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileResourceSoapService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.*;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.ArtWorkDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.ArtworkImageBase64RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.DonationConfigResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.UiDataListRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.MOBILE_RESOURCE_SERVICE;


@Service
public class MobileResourceSoapServiceImpl implements MobileResourceSoapService {

    private static final String SERVICE_KEY = "mobileResource";

    private static final Logger log = LogManager.getLogger(MobileResourceSoapServiceImpl.class);

    @Autowired
    private MobileResourceSoapClientRegistry soapClientRegistry;

    private final ObjectFactory factory = new ObjectFactory();

    private WebServiceMessageCallback withHeader(LanguageEnum langEnum) {
        return message -> {
            try {
                AcceptLanguages header = new AcceptLanguages();
                header.setLanguage(LanguageEnum.getCbtTypeFromEnum(langEnum).orElse(LanguageEnum.EN.getCbtType()));

                JAXBElement<AcceptLanguages> headerElement = new JAXBElement<>(
                        new QName("http://www.rta.ae/ActiveMatrix/ESB/AcceptLanguage/XMLSchema/", "AcceptLanguages"),
                        AcceptLanguages.class,
                        header
                );

                soapClientRegistry.getTemplate(SERVICE_KEY)
                        .getMarshaller()
                        .marshal(headerElement, ((SoapMessage) message).getSoapHeader().getResult());
            } catch (Exception e) {
                throw new RuntimeException("Failed to add SOAP header", e);
            }
        };
    }


    public CbtUiDataListResponseDTO getUIList(LanguageEnum language, UiDataListRequestDTO request) {
        try {

            UiListRequest soapRequest = new UiListRequest();
            ListType type = UiListTypeMapper(request);
            log.info("#################    request for UiDataList :: {}    ####################", request);
            log.info("########################    request for List Type :: {}    ######################", type);
            soapRequest.setType(type);
            JAXBElement<UiListRequest> jaxbRequest = factory.createUiListRequest(soapRequest);
            log.debug("Created JAXB Request: {}", jaxbRequest);
            WebServiceTemplate template = soapClientRegistry.getTemplate(SERVICE_KEY);
            @SuppressWarnings(value = "unchecked")
            JAXBElement<UiListResponse> jaxbResponse = (JAXBElement<UiListResponse>) template.marshalSendAndReceive(MOBILE_RESOURCE_SERVICE, jaxbRequest, withHeader(language));
            log.info("Received UiListResponse from SOAP: {}", jaxbResponse);
            return createUIListResponse(jaxbResponse.getValue(), language);

        } catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred in getUiList with fault code{}", ex.getFaultCode(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }

    }

    private CbtUiDataListResponseDTO createUIListResponse(UiListResponse response, LanguageEnum language) {
        CbtUiDataListResponseDTO dto = new CbtUiDataListResponseDTO();
        // Map specialLayouts to artWorks
        if (response != null && response.getSpecialLayouts() != null) {
            dto.setArtWorks(
                    response.getSpecialLayouts().stream()
                            .map(specialLayout -> mapToArtWorkDTO(specialLayout , language))
                            .filter(Objects::nonNull)
                            .toList()
            );
        }

        if (response != null && response.getMap() != null && response.getMap().getEntry() != null) {
            if (response.getType() == null) return dto;
            for (UiListResponse.Map.Entry entry : response.getMap().getEntry()) {
                if (entry == null || entry.getKey() == null || entry.getValue() == null) continue;
                String key = entry.getKey().trim();
                String value = entry.getValue().trim();
                switch (response.getType()) {
                    case EMIRATE -> {
                        if (dto.getEmirates() == null) dto.setEmirates(new ArrayList<>());
                        //dto.getEmirates().add(new CbtEmiratesDTO(key,value));
                        dto.getEmirates().add(mapToCbtEmirates(key, value, language));
                    }

                    case AREA_CODE -> {
                        if (dto.getAreaCodes() == null) dto.setAreaCodes(new ArrayList<>());
                        dto.getAreaCodes().add(key);
                    }

                    case OFFICE_AREA_CODE -> {
                        if (dto.getOfficeAreaCodes() == null) dto.setOfficeAreaCodes(new ArrayList<>());
                        dto.getOfficeAreaCodes().add(key);
                    }

                    case COUNTRY_CODE -> {
                        if (dto.getCountryCodes() == null) dto.setCountryCodes(new ArrayList<>());
                        dto.getCountryCodes().add(mapToCountryCode(key, value, language));
                    }

                    case NATIONALITY -> {
                        if (dto.getNationalities() == null) dto.setNationalities(new ArrayList<>());
                        dto.getNationalities().add(mapToNationality(key, value, language));
                    }

                    case TITLE -> {
                        if (dto.getSalutations() == null) dto.setSalutations(new ArrayList<>());
                        dto.getSalutations().add(mapToSalutation(key, value, language));
                    }

                    case PASSENGER_CLASS -> {
                        if (dto.getServiceClasses() == null) dto.setServiceClasses(new ArrayList<>());
                        dto.getServiceClasses().add(mapToServiceClass(key, value, language));
                    }

                    case CONCESSION_TYPE -> {
                        if (dto.getConcessionsTypes() == null) dto.setConcessionsTypes(new ArrayList<>());
                        dto.getConcessionsTypes().add(mapToConcessionType(key, value, language));
                    }
                }
            }
        }
        return dto;

    }

    private CbtConcessionTypeDTO mapToConcessionType(String key, String value, LanguageEnum language) {
        CbtConcessionTypeDTO concessionTypeDTO = new CbtConcessionTypeDTO();
        concessionTypeDTO.setId(key);
        if (LanguageEnum.EN == language) concessionTypeDTO.setNameEn(value);
        else if (LanguageEnum.AR == language) concessionTypeDTO.setNameAr(value);
        return concessionTypeDTO;
    }


    private CbtServiceClassDTO mapToServiceClass(String key, String value, LanguageEnum language) {
        CbtServiceClassDTO serviceClassDTO = new CbtServiceClassDTO();
        serviceClassDTO.setId(key);
        if (LanguageEnum.EN == language) serviceClassDTO.setNameEn(value);
        else if (LanguageEnum.AR == language) serviceClassDTO.setNameAr(value);
        return serviceClassDTO;
    }

    private CbtSalutationDTO mapToSalutation(String key, String value, LanguageEnum language) {
        CbtSalutationDTO salutationDTO = new CbtSalutationDTO();
        salutationDTO.setId(key);
        if (LanguageEnum.EN == language) salutationDTO.setNameEn(value);
        else if (LanguageEnum.AR == language) salutationDTO.setNameAr(value);
        return salutationDTO;
    }

    private CbtEmiratesDTO mapToCbtEmirates(String key, String value, LanguageEnum clientLanguage) {
        CbtEmiratesDTO cbtEmiratesDTO = new CbtEmiratesDTO();
        cbtEmiratesDTO.setId(key);
        if (LanguageEnum.EN == clientLanguage) cbtEmiratesDTO.setNameEn(value);
        else if (LanguageEnum.AR == clientLanguage) cbtEmiratesDTO.setNameAr(value);
        return cbtEmiratesDTO;
    }

//    private CountryDTO mapToCountryCode(String key, String value, LanguageEnum lang) {
//        return new CountryDTO(value, lang == LanguageEnum.EN ? key : null, lang != LanguageEnum.EN ? key : null);
//    }

    private CbtCountryCodesDTO mapToCountryCode(String key, String value, LanguageEnum lang) {
        CbtCountryCodesDTO cbtCountryCodesDTO = new CbtCountryCodesDTO();
        cbtCountryCodesDTO.setId(key);
        if (LanguageEnum.EN == lang) cbtCountryCodesDTO.setValueEn(value);
        else if (LanguageEnum.AR == lang) cbtCountryCodesDTO.setValueAr(value);
        return cbtCountryCodesDTO;
    }

    private NationalityDTO mapToNationality(String code, String value, LanguageEnum lang) {
        NationalityDTO nationalityDTO = new NationalityDTO();
        nationalityDTO.setId(code);
        if (LanguageEnum.EN == lang) nationalityDTO.setNameEn(value);
        else if (LanguageEnum.AR == lang) nationalityDTO.setNameAr(value);
        return nationalityDTO;
    }

    private ArtWorkDTO mapToArtWorkDTO(SpecialLayout layout , LanguageEnum language) {
        if (layout == null) return null;

        return new ArtWorkDTO(
                layout.getLayoutId() != null ? layout.getLayoutId().toString() : null,
                layout.getEnglishName(),
                layout.getArabicName(),
                LanguageEnum.EN.equals(language) ? layout.getShortName() : null,
                LanguageEnum.AR.equals(language) ? layout.getShortName() : null,
                layout.getCategoryId() != null ? layout.getCategoryId().toString() : null,
                layout.getTypeId() != null ? layout.getTypeId().toString() : null,
                layout.getDesignFee(),
                layout.getLastUpdateTime() != null ?
                        toLocalDateTime(layout.getLastUpdateTime()) : null
        );
    }

    private LocalDateTime toLocalDateTime(XMLGregorianCalendar xmlCal) {
        return xmlCal.toGregorianCalendar().toZonedDateTime().toLocalDateTime();
    }

    private <E extends Enum<E>> void addEnumSafe(List<E> list, Class<E> enumType, String value) {
        list.add(Enum.valueOf(enumType, value.toUpperCase()));
    }

    private ListType UiListTypeMapper(UiDataListRequestDTO req) {
        if (req.getAreaCodeRequired() == YesOrNoEnum.YES) return ListType.AREA_CODE;
        if (req.getConcessionTypeRequired() == YesOrNoEnum.YES) return ListType.CONCESSION_TYPE;
        if (req.getEmirateRequired() == YesOrNoEnum.YES) return ListType.EMIRATE;
        if (req.getNationalityRequired() == YesOrNoEnum.YES) return ListType.NATIONALITY;
        if (req.getOfficeAreaCodeRequired() == YesOrNoEnum.YES) return ListType.OFFICE_AREA_CODE;
        if (req.getSpecialLayoutRequired() == YesOrNoEnum.YES) return ListType.SPECIAL_LAYOUT;
        if (req.getTitleRequired() == YesOrNoEnum.YES) return ListType.TITLE;
        if (req.getServiceClassRequired() == YesOrNoEnum.YES) return ListType.PASSENGER_CLASS;

        return null;
    }


    public String getImageInBase64(LanguageEnum language, ArtworkImageBase64RequestDTO request) {
        try {

            UiImageRequest soapRequest = new UiImageRequest();
            if (request.getImageFetchType() != null)
                soapRequest.setSize(ImageSize.fromValue(request.getImageFetchType().toString()));
            if (request.getImageId() != null) soapRequest.setImageId(Integer.valueOf(request.getImageId()));
            if (request.getSpecialLayout() != null && request.getSpecialLayout() == YesOrNoEnum.YES)
                soapRequest.setType(ImageType.SPECIAL_LAYOUT);
            JAXBElement<UiImageRequest> jaxbRequest = factory.createUiImageRequest(soapRequest);
            WebServiceTemplate template = soapClientRegistry.getTemplate(SERVICE_KEY);
            @SuppressWarnings(value = "unchecked")
            JAXBElement<String> jaxbResponse = (JAXBElement<String>) template.marshalSendAndReceive(MOBILE_RESOURCE_SERVICE, jaxbRequest, withHeader(language));

            return jaxbResponse.getValue();

        } catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred in getImageInBase64 with fault code{}", ex.getFaultCode(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }

    }

    public DonationConfigResponseDTO getDonationConfiguration(LanguageEnum language) {

        try {
            DonationConfigurationRequest soapRequest = new DonationConfigurationRequest();

            JAXBElement<DonationConfigurationRequest> jaxbRequest = new JAXBElement<>(
                    new QName("http://www.rta.ae/ActiveMatrix/ESB/ResourcesService/XMLSchema", "donationConfigurationRequest"),
                    DonationConfigurationRequest.class,
                    soapRequest
            );

            WebServiceTemplate template = soapClientRegistry.getTemplate(SERVICE_KEY);
            @SuppressWarnings("unchecked")
            JAXBElement<DonationConfigurationResponse> jaxbResponse =
                    (JAXBElement<DonationConfigurationResponse>) template.marshalSendAndReceive(MOBILE_RESOURCE_SERVICE, jaxbRequest, withHeader(language));

            return createDonationConfigurationResponse(jaxbResponse.getValue());

        } catch (SoapFaultClientException ex) {
            log.info("Soap Exception occurred getDonationConfig with fault code{}", ex.getFaultCode(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }


    }

    private DonationConfigResponseDTO createDonationConfigurationResponse(DonationConfigurationResponse donationConfigurationResponse) {
        DonationConfigResponseDTO responseDTO = new DonationConfigResponseDTO();
        responseDTO.setAmount(donationConfigurationResponse.getAmount());
        responseDTO.setAvailable(donationConfigurationResponse.isAvailable() ? YesOrNoEnum.YES : YesOrNoEnum.NO);
        return responseDTO;
    }

}
