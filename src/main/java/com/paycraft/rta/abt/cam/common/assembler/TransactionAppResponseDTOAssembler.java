package com.paycraft.rta.abt.cam.common.assembler;

import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import org.springframework.stereotype.Component;

@Component
public class TransactionAppResponseDTOAssembler<T> {

    public TransactionalAppResponseDTO<T> getResponse(T t, MessageKeyEnum messageKey, String transactionId) {
        return TransactionalAppResponseDTO.of(transactionId,messageKey.getCode().toString(), messageKey.getMessage(), null, t);
    }

    public TransactionalAppResponseDTO<T> getResponse(T t, String code, String message, String transactionId) {
        return TransactionalAppResponseDTO.of(transactionId,code, message, null, t);
    }
}
