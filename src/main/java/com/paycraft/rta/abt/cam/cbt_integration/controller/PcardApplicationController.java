package com.paycraft.rta.abt.cam.cbt_integration.controller;

import com.paycraft.rta.abt.cam.cbt_integration.service.impl.PcardApplicationServiceImpl;
import com.paycraft.rta.abt.cam.cbt_integration.service.PcardApplicationService;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.*;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.*;


@RestController
@RequestMapping("/pcard/application")
public class PcardApplicationController {

    @Autowired
    private PcardApplicationServiceImpl pcardApplicationServiceImpl;

    @Autowired
    private PcardApplicationService pcardApplicationService;

    private static final Logger log = LogManager.getLogger(PcardApplicationController.class);

    @PostMapping(PCARD_REVIEW)
    public ResponseEntity<TransactionalAppResponseDTO<PcardReviewResponseDTO>> reviewPcard(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody PcardReviewSubmitRequestDTO requestDTO) {
        log.info("Request for Personalised card Review : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(pcardApplicationService.reviewPcard(requestDTO,clientLanguage));
    }

    @PostMapping(PCARD_CONFIRM)
    public ResponseEntity<TransactionalAppResponseDTO<PcardConfirmResponseDTO>> confirmPcard(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody PcardConfirmRequestDTO requestDTO) {
        log.info("Request for Confirm Personalised card : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(pcardApplicationService.confirmPcard(clientLanguage,requestDTO));
    }

    @PostMapping(PCARD_SUBMIT)
    public ResponseEntity<TransactionalAppResponseDTO<PcardSubmitResponseDTO>> submitPcard(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody PcardReviewSubmitRequestDTO requestDTO) {
        log.info("Request for Submit Personalised Card Request : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(pcardApplicationService.submitPcard(clientLanguage,requestDTO));
    }

    @PostMapping(PCARD_PAYMENT_PARAMS)
    @Operation(summary = "To get the payment parameters/amount for Personalise Card Application")
    public ResponseEntity<TransactionalAppResponseDTO<PaymentParamsResponseDTO>> fetchPcardPaymentParams(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @Valid @RequestBody PaymentParamsRequestDTO requestDTO) {
        log.info("Request for Fetch Personalised card Payment Params : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(pcardApplicationService.fetchPcardPaymentParams(clientLanguage,requestDTO));
    }

    @PostMapping(PCARD_TRACK)
    @Operation(summary = "Track Personalise Nol Card Application")
    public ResponseEntity<TransactionalAppResponseDTO<TrackPcardResponseDTO>> trackPcard(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @Valid @RequestBody TrackPcardRequestDTO requestDTO) {
        log.info("Request for Track Personalised Card : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(pcardApplicationService.trackPcard(clientLanguage,requestDTO));
    }

}
