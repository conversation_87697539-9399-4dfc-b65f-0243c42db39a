package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.StudentConcessionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CardInfoResponseDTO {
    private String nolCardId;
    private String isicSerialNumber;
    private BigDecimal balance;
    private String status;
    private String cardHolderName;
    private String cardExpiry;
    private CardTypeEnum cardType;
    private YesOrNoEnum balanceProtectionStatus;
    private BigDecimal pendingAmount;
    private String mobileUserId;
    private ServiceClassEnum serviceClass;
    private ConcessionTypeEnum concessionType;
    private LocalDateTime concessionExpiryDate;
    private String artWorkId;
    private FareMediaFormFactorEnum fareMediaFormFactor;
    private YesOrNoEnum autoTopUpEnabled;
    private StudentConcessionDetailsDTO studentConcessionDetails;
    private MobileNumberDTO mobileNumber;
    private String email;
    private YesOrNoEnum isPinValid;
    private YesOrNoEnum isBlackListed;
    private String emiratesId;
    private LocalDate dateOfBirth;
    private String passportNumber;
}
