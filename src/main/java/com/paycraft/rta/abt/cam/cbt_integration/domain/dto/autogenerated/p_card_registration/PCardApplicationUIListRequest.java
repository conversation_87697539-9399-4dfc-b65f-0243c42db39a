//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import java.util.ArrayList;
import java.util.List;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.ListType;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.PCardApplicationUIListParameter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for pCardApplicationUIListRequest complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="pCardApplicationUIListRequest"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="parameters" type="{http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema}pCardApplicationUIListParameter" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="type" type="{http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema}listType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pCardApplicationUIListRequest", propOrder = {
    "parameters",
    "type"
})
public class PCardApplicationUIListRequest {

    @XmlElement(nillable = true)
    protected List<PCardApplicationUIListParameter> parameters;
    @XmlSchemaType(name = "string")
protected ListType type;

    /**
     * Gets the value of the parameters property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a <CODE>set</CODE> method for the parameters property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getParameters().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PCardApplicationUIListParameter }
     * 
     * 
     */
    public List<PCardApplicationUIListParameter> getParameters() {
        if (parameters == null) {
            parameters = new ArrayList<PCardApplicationUIListParameter>();
        }
        return this.parameters;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link ListType }
     *     
     */
public ListType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link ListType }
     *     
     */
    public void setType(ListType value) {
        this.type = value;
    }

}
