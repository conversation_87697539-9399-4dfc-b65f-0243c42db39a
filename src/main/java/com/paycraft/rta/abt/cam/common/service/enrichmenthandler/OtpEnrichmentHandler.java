package com.paycraft.rta.abt.cam.common.service.enrichmenthandler;

import com.paycraft.rta.abt.cam.common.configurables.Configurable;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.SendOtpRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.CamOtpDetails;
import com.paycraft.rta.abt.cam.common.domain.enums.OtpStatusEnum;
import com.paycraft.rta.abt.cam.common.utils.DateTimeUtils;
import com.paycraft.rta.abt.cam.common.utils.OtpUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.BiConsumer;

import static com.paycraft.rta.abt.cam.common.domain.constants.AppConstants.EXPIRY_TIME;

@Component
public class OtpEnrichmentHandler {



    public  final BiConsumer<CamOtpDetails,SendOtpRequestDTO> enrichOtpDetails =(otpDetails, requestDTO)  -> {
        otpDetails.setOtp(OtpUtils.generateOtp());
        otpDetails.setStatus( OtpStatusEnum.ACTIVE.getId());
        otpDetails.setTransactionReferenceId(OtpUtils.generateOtpIdentifier());
        if (Objects.nonNull(requestDTO.getMobileNumber())) {
            otpDetails.setMobileCountryCode(requestDTO.getMobileNumber().getCountryCode());
            otpDetails.setMobileNumber(requestDTO.getMobileNumber().getNumber());
        }
        otpDetails.setEmailId(requestDTO.getEmail());
        LocalDateTime generatedAt = DateTimeUtils.getCurrentUTCTime();
        LocalDateTime expiredAt= generatedAt.plusSeconds(Long.parseLong(Configurable.getDataByTdKey(EXPIRY_TIME)));
        otpDetails.setActionName(requestDTO.getActionType());
        otpDetails.setOtpGeneratedTime(Timestamp.valueOf(generatedAt));
        otpDetails.setOtpExpiryTime(Timestamp.valueOf(expiredAt));
        otpDetails.setEmailId(requestDTO.getEmail());
    };

}
