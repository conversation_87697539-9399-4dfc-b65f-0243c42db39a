package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtNationality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CamMstCbtNationalityRepository extends JpaRepository<CamMstCbtNationality, Integer> {

    Optional<CamMstCbtNationality> findByCbtId(String cbtId);

    List<CamMstCbtNationality> findByLanguage(String languageCode);  // This works without @Query

    @Query("SELECT c FROM CamMstCbtNationality c WHERE c.cbtId IS NOT NULL")
    List<CamMstCbtNationality> findAllWhereCbtIdNotNull();}
