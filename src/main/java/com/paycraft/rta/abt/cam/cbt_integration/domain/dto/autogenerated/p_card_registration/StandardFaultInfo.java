//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for standardFaultInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="standardFaultInfo"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="businessViolations" type="{http://www.rta.ae/ActiveMatrix/ESB/StandardFaultException/XMLSchema}businessValidationResult" minOccurs="0"/&gt;
 *         &lt;element name="exceptionReferenceId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="fieldValidations" type="{http://www.rta.ae/ActiveMatrix/ESB/StandardFaultException/XMLSchema}fieldError" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "standardFaultInfo", namespace = "http://www.rta.ae/ActiveMatrix/ESB/StandardFaultException/XMLSchema", propOrder = {
    "businessViolations",
    "exceptionReferenceId",
    "fieldValidations"
})
public class StandardFaultInfo {

protected BusinessValidationResult businessViolations;
    protected String exceptionReferenceId;
    @XmlElement(nillable = true)
    protected List<FieldError> fieldValidations;

    /**
     * Gets the value of the businessViolations property.
     * 
     * @return
     *     possible object is
     *     {@link BusinessValidationResult }
     *     
     */
    public BusinessValidationResult getBusinessViolations() {
        return businessViolations;
    }

    /**
     * Sets the value of the businessViolations property.
     * 
     * @param value
     *     allowed object is
     *     {@link BusinessValidationResult }
     *     
     */
    public void setBusinessViolations(BusinessValidationResult value) {
        this.businessViolations = value;
    }

    /**
     * Gets the value of the exceptionReferenceId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExceptionReferenceId() {
        return exceptionReferenceId;
    }

    /**
     * Sets the value of the exceptionReferenceId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExceptionReferenceId(String value) {
        this.exceptionReferenceId = value;
    }

    /**
     * Gets the value of the fieldValidations property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a <CODE>set</CODE> method for the fieldValidations property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getFieldValidations().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FieldError }
     * 
     * 
     */
    public List<FieldError> getFieldValidations() {
        if (fieldValidations == null) {
            fieldValidations = new ArrayList<FieldError>();
        }
        return this.fieldValidations;
    }

}
