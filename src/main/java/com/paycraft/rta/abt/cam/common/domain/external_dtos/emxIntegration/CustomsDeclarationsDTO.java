package com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomsDeclarationsDTO {
    private String reference;
    private String description;
    private String countryOfOrigin;
    private Integer weight;
    private BaseDimensionDTO dimensions;
    private Integer quantity;
    private String hsCode;
    private BigDecimal value;
    private String currencyCode;
}