//package com.paycraft.rta.abt.cam.common.utils;
//
//import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
//import org.apache.poi.ss.usermodel.*;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//
//import java.io.FileOutputStream;
//import java.io.IOException;
//
//public class ExcelExporter {
//    public static void main(String[] args) {
//        Workbook workbook = new XSSFWorkbook();
//        Sheet sheet = workbook.createSheet("Error Codes");
//
//        // Header
//        Row header = sheet.createRow(0);
//        header.createCell(0).setCellValue("Enum Name");
//        header.createCell(1).setCellValue("Message");
//        header.createCell(2).setCellValue("Code");
//        header.createCell(3).setCellValue("Internal Code");
//
//        // Fill data
//        int rowNum = 1;
//        for (ErrorCodesEnum error : ErrorCodesEnum.values()) {
//            Row row = sheet.createRow(rowNum++);
//            row.createCell(0).setCellValue(error.name());
//            row.createCell(1).setCellValue(error.getMessage());
//            row.createCell(2).setCellValue(error.getErrorCode());
//            row.createCell(3).setCellValue(error.getCbtErrorCode() == null ? "null" : error.getCbtErrorCode());
//        }
//
//        // Autosize
//        for (int i = 0; i < 4; i++) sheet.autoSizeColumn(i);
//
//        // Save file
//        try (FileOutputStream fileOut = new FileOutputStream("D:\\ErrorCodesEnum.xlsx")) {
//            workbook.write(fileOut);
//            workbook.close();
//            System.out.println("Excel file written successfully!");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//}
//
