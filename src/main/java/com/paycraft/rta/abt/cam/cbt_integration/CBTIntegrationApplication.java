package com.paycraft.rta.abt.cam.cbt_integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration;
import org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration;
import org.springframework.boot.autoconfigure.webservices.client.WebServiceTemplateAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(exclude = { HttpClientAutoConfiguration.class, RestClientAutoConfiguration.class, RestTemplateAutoConfiguration.class, WebServiceTemplateAutoConfiguration.class,
		WebClientAutoConfiguration.class})
@ComponentScan(basePackages = {"com.paycraft.rta.abt.cam.cbt_integration", "com.paycraft.rta.abt.cam.common"})
@EnableJpaRepositories(basePackages = {"com.paycraft.rta.abt.cam.common.repository"})
@EntityScan(basePackages = "com.paycraft.rta.abt.cam.common.domain.entities")
@EnableAsync
public class CBTIntegrationApplication {

	public static void main(String[] args) {
		SpringApplication.run(CBTIntegrationApplication.class, args);
	}

}
