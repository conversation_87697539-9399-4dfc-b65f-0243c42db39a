package com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.WeightDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateShipmentRequestDTO {

    private WeightDTO weight; // Mandatory
    private PartyDTO shipper; // few Mandatory
    private PartyDTO consignee;  // few Mandatory
    private DimensionsDTO dimensions;  // Optional
    private AccountDTO account; // Mandatory
    private String productCode; // Mandatory (EMX-International,ETOEPARCEL,International,Domestic) TODO : convert to enum
    private String serviceType; // Mandatory (Domestic,International,EMS, None,SameDay,PrimeRegistered,PrimeTracked,PrimeExpres,Registered,Parcel,EconomyParcel,Return, Standard) TODO : Enum
    private String printType;   // Mandatory (LabelOnly,AWBO<PERSON>ly,AWBAndLabel,None)
    private boolean sendMailToSender;
    private boolean sendMailToReceiver;
    private boolean isInsured;
    private List<CustomsDeclarationsDTO> customsDeclarations;
    private ValuesDTO declaredValue;
    private Integer numberOfPieces;  // Mandatory
    private String referenceNumber1;
    private String referenceNumber2;
    private String referenceNumber3;
    private String referenceNumber4;
    private String specialNotes;
    private String remarks;
    private String branchName;
    private String deliveryType;  // Mandatory (DoorToDoor,Counter, PUDO)
    private String contentType; // Mandatory (Document,NonDocument)
    private String requestType;
    private String awbNumber;
    private String paymentType;
    private String commodityDescription;
    private LocalDate preferredPickupDate;
    private LocalTime preferredPickupTimeFrom;
    private LocalTime preferredPickupTimeTo;
    private String deliveredDuty;
    private Integer locationId;
    private boolean isDropOff;
    private String dropOffOfficeId;
    private boolean isReturnService;
    private boolean isCod;
    private ValuesDTO coDAmount;  // few Mandatory

}
