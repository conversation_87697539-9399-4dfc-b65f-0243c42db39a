package com.paycraft.rta.abt.cam.common.domain.entities.tibco;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
        import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "CAM_MST_CBT_SERVICE_CLASS")
@IdClass(CamMstCbtServiceClassId.class)
public class CamMstCbtServiceClass {

    @Id
    @Column(name = "ID", length = 50)
    private String id;

    @Column(name = "TYPE_EN", length = 100)
    private String typeEn;

    @Column(name = "TYPE_AR", length = 100)
    private String typeAr;

    @Id
    private String language;

    @Column(name = "CREATED_BY", length = 50)
    private String createdBy;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "UPDATED_BY", length = 50)
    private String updatedBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;
}
