package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.ext_aCard_registration_service.*;


import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nolcard_personalise.CustomHeader;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.ExtAcardRegistrationService;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardKeyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card.RegisterCardExHouseConfirmRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card.RegisterCardExHouseConfirmResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.oxm.Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapHeader;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamSource;
import java.io.IOException;
import java.io.StringReader;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.EXT_ACARD_REGISTRATION_SERVICE;


@Service
public class ExtAcardResgistrationServiceImpl implements ExtAcardRegistrationService {

    private static final Logger log = LogManager.getLogger(ExtAcardResgistrationServiceImpl.class);
    private static final String SERVICE_KEY = "extACardRegistrationExternal";

    @Autowired
    SoapClientRegistry soapClientRegistry;

    private final ObjectFactory factory = new ObjectFactory();

    @Autowired
    private TransactionAppResponseDTOAssembler assembler;
    @Autowired
    private MobileCardEnquiryService mobileCardEnquiryService;


//    private WebServiceMessageCallback withHeader(LanguageEnum langEnum) {
//        return message -> {
//            try {
//                AcceptLanguages header = new AcceptLanguages();
//                header.setLanguage(LanguageEnum.getCbtTypeFromEnum(langEnum).orElse(LanguageEnum.EN.getCbtType()));
//
//                JAXBElement<AcceptLanguages> headerElement = new JAXBElement<>(new QName("http://www.rta.ae/ActiveMatrix/ESB/AcceptLanguage/XMLSchema/", "AcceptLanguages"), AcceptLanguages.class, header);
//
//                soapClientRegistry.getTemplate(SERVICE_KEY).getMarshaller().marshal(headerElement, ((SoapMessage) message).getSoapHeader().getResult());
//            } catch (Exception e) {
//                throw new RuntimeException("Failed to add SOAP header", e);
//            }
//        };
//    }

       private WebServiceMessageCallback withHeader(LanguageEnum languageEnum) {
        return message -> {
            SoapMessage soapMessage = (SoapMessage) message;
            SoapHeader header = soapMessage.getSoapHeader();

            String headerXml = String.format(
                    "<ns:AcceptLanguages xmlns:ns=\"http://www.rta.ae/ActiveMatrix/ESB/AcceptLanguage/XMLSchema/\">" +
                            "<ns:Language>%s</ns:Language>" +
                            "</ns:AcceptLanguages>",
                    languageEnum.getCbtType()
            );
            try {
                Transformer transformer = TransformerFactory.newInstance().newTransformer();
                Source source = new StreamSource(new StringReader(headerXml));
                Result result = header.getResult();
                transformer.transform(source, result);
            } catch (Exception e) {
                throw new IOException("Unable to write SOAP header", e);
            }
        };
    }
    @Override
    public TransactionalAppResponseDTO<RegisterCardExHouseConfirmResponseDTO> registerCardRequestExHouseConfirm(RegisterCardExHouseConfirmRequestDTO requestDTO, LanguageEnum languageEnum) {
        try {
            CardKeyDTO cardKeyDTO = mobileCardEnquiryService.findCardKey(requestDTO.getNolCardId(), requestDTO.getIamUserId(), languageEnum);
            ThirdPartyACardRegistrationRequest soapRequest = new ThirdPartyACardRegistrationRequest();

            if (requestDTO != null) {
                soapRequest.setReferenceID(requestDTO.getReferenceId());

                if (requestDTO.getBeId() != null) {
                    soapRequest.setBusinessEntityID(Integer.valueOf(String.valueOf(requestDTO.getBeId())));
                }

                if ((cardKeyDTO != null) && (cardKeyDTO.getCardId() != null)) { //uncomment this after changes are made in common
                soapRequest.setCardID(String.valueOf(cardKeyDTO.getCardId()));
                }

                if (requestDTO.getNolCardId() != null) {
                    soapRequest.setCardTagId(Long.valueOf(requestDTO.getNolCardId()));
                }

                soapRequest.setCardIteration(requestDTO.getCardIteration());

                if (requestDTO.getEmail() != null) {
                    soapRequest.setEmail(requestDTO.getEmail());
                }

                if (requestDTO.getEmiratesId() != null) {
                    soapRequest.setEmiratesID(requestDTO.getEmiratesId());
                }

                if (requestDTO.getGender() != null && requestDTO.getGender().getId() != null) {
                    soapRequest.setGender(requestDTO.getGender().getId());
                }

                if (requestDTO.getIsCardPresented() != null && requestDTO.getIsCardPresented().getId() != null) {
                    soapRequest.setIsCardPresented(requestDTO.getIsCardPresented().getId());
                }

                if (requestDTO.getDateOfBirth() != null) {
                    GregorianCalendar calendar = GregorianCalendar.from(requestDTO.getDateOfBirth().atStartOfDay(ZoneId.systemDefault()));
                    try {
                        XMLGregorianCalendar xmlDateOfBirth = DatatypeFactory.newInstance().newXMLGregorianCalendar(calendar);
                        soapRequest.setDateOfBirth(xmlDateOfBirth);
                    } catch (DatatypeConfigurationException e) {
                        log.error("Error converting dateOfBirth to XMLGregorianCalendar", e);
                    }
                }

                if (requestDTO.getFirstNameEn() != null || requestDTO.getSecondNameEn() != null || requestDTO.getLastNameEn() != null) {
                    CardApplicantIndividualFullName englishName = new CardApplicantIndividualFullName();
                    englishName.setFirst(requestDTO.getFirstNameEn());
                    englishName.setMiddle(requestDTO.getSecondNameEn());
                    englishName.setLast(requestDTO.getLastNameEn());
                    soapRequest.setEnglishName(englishName);
                }

                if (requestDTO.getFirstNameAr() != null || requestDTO.getSecondNameAr() != null || requestDTO.getLastNameAr() != null) {
                    CardApplicantIndividualFullName arabicName = new CardApplicantIndividualFullName();
                    arabicName.setFirst(requestDTO.getFirstNameAr());
                    arabicName.setMiddle(requestDTO.getSecondNameAr());
                    arabicName.setLast(requestDTO.getLastNameAr());
                    soapRequest.setArabicName(arabicName);
                }

                if (requestDTO.getMobileNumber() != null) {
                    PhoneNumber phone = new PhoneNumber();
                    if (requestDTO.getMobileNumber().getCountryCode() != null)
                        phone.setCountryCode(requestDTO.getMobileNumber().getCountryCode());
                    if (requestDTO.getMobileNumber().getAreaCode() != null)
                        phone.setAreaCode(requestDTO.getMobileNumber().getAreaCode());
                    if (requestDTO.getMobileNumber().getNumber() != null)
                        phone.setNumber(requestDTO.getMobileNumber().getNumber());
                    soapRequest.setPhoneNumber(phone);
                }
            }

            WebServiceMessageCallback callback = buildCallbackWithHeader("registerAcard",withHeader(languageEnum));

            JAXBElement<ThirdPartyACardRegistrationRequest> jaxbRequest = factory.createExtACardRegistrationRequest(soapRequest);
            log.info("Sending SOAP request to EXT_ACARD_REGISTRATION_SERVICE...:{}",jaxbRequest);
            JAXBElement<ThirdPartyACardRegistrationResponse> jaxbResponse = (JAXBElement<ThirdPartyACardRegistrationResponse>) getTemplate().marshalSendAndReceive(EXT_ACARD_REGISTRATION_SERVICE,jaxbRequest,callback);

            ThirdPartyACardRegistrationResponse response = jaxbResponse.getValue();
            log.info("Received response from CBT for registerCardRequestExHouseConfirm: {}", response);
            if (!response.isSuccess()) {
                List<ValidationResponseDTO> violations = new ArrayList<>();
                if (response.getViolations() != null) {
                    for (BusinessViolation violation : response.getViolations()) {
                        String cbtCode = violation.getViolationCode();
                        ErrorCodesEnum matchedError = ErrorCodesEnum.fromCbtCode(cbtCode).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(matchedError.getErrorCode(), violation.getMessage()));
                    }

                }
                throw new CbtIntegrationException(null, violations , MessageKeyEnum.VALIDATION_FAILED.getCode() ,MessageKeyEnum.VALIDATION_FAILED.getMessage());
            }
            RegisterCardExHouseConfirmResponseDTO responseDTO= new RegisterCardExHouseConfirmResponseDTO();
            responseDTO.setReferenceId(response.getReferenceId());
            return assembler.getResponse(responseDTO, MessageKeyEnum.SUCCESS, requestDTO.getTransactionId());

        } catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException occurred in registerCardRequestExHouseConfirm(). FaultCode: {}, FaultMessage: {}", ex.getFaultCode(), ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(Enum::name).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            String exceptionRefId = null;

            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            if (faultInfo != null) {
                exceptionRefId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null) {
                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum error = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(error.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }

            throw new CbtIntegrationException(exceptionRefId, violations, faultCode, faultMessage);
        }
    }
    private WebServiceMessageCallback buildCallbackWithHeader(String actionName, WebServiceMessageCallback headerCallback) {
        SoapActionCallback actionCallback = new SoapActionCallback(actionName);

        return message -> {
            // Call the logic in your `withHeader(...)` to manually write header
            headerCallback.doWithMessage(message);

            // Set the SOAP action
            actionCallback.doWithMessage(message);
        };
    }
    private WebServiceTemplate getTemplate() {
        return soapClientRegistry.getTemplate(SERVICE_KEY);
    }
}
