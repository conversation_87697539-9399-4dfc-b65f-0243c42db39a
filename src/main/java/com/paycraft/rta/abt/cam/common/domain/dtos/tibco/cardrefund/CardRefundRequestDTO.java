package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardrefund;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.AddressDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.BlockReasonEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.RefundModeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CardRefundRequestDTO extends CardRequestDTO {
    @Valid
    @NotNull
    private MobileNumberDTO mobileNumber;
    @Valid
    private MobileNumberDTO alternateMobileNumber;
    @NotBlank
    @Email
    private String email;
    @NotBlank
    private String pin;
    @NotBlank
    private BlockReasonEnum reason;
    private YesOrNoEnum isCardRetained;
    @NotBlank
    private RefundModeEnum refundMode;
    private String transferNolCardId;
    private String locationId;
    private YesOrNoEnum isIsicEnabled;
    private String isicSerialNumber;
    private String schoolShortName;
    @Valid
    private AddressDTO address;
    private String documentId;
    private String documentGroupReferenceId;
}
