package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;

@XmlType(name = "code")
@XmlEnum
public enum Code {
    HOLD ,
    REJECTED,
    PROCESSING,
    APPROVED,
    DISPATCHED,
    CLOSED,
    PAYMENT;

    public String value() {
        return name();
    }

    public static Code fromValue(String v) {
        return valueOf(v);
    }
}
