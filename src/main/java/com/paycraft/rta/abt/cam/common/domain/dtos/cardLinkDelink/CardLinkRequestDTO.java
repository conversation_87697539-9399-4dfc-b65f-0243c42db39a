package com.paycraft.rta.abt.cam.common.domain.dtos.cardLinkDelink;


import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.GuestUserInfo;
import com.paycraft.rta.abt.cam.common.domain.enums.RequestChannelEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardLinkRequestDTO implements Serializable {

    private long beId;
    @NotNull
    private RequestChannelEnum requestChannel;
    @NotBlank
    private String transactionId;

    private String iamUserId;

    private String uaePassUserId;

    private String abtAccountId;
    @Valid
    private GuestUserInfo guestUserInfo;
    @NotBlank
    private String nolCardId;

    private String cardToken;
    @NotBlank
    private String pin;
}