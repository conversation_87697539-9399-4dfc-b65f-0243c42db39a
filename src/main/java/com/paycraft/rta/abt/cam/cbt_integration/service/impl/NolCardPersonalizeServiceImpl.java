package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nolcard_personalise.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.NolCardPersonalizeService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.PaymentDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.LineItemResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card.*;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.oxm.Marshaller;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.NOL_CARD_PERSONALIZE;

@Slf4j
@Service
public class NolCardPersonalizeServiceImpl implements NolCardPersonalizeService {

    private static String SERVICE_KEY="nolCardPersonalize";
    @Autowired
    SoapClientRegistry soapClientRegistry;

    private static final String HEADER_NAMESPACE = "http://www.rta.ae/ActiveMatrix/ESB/CustomHeader/XMLSchema";

    public RegisterCardReviewResponseDTO reviewNolcardPersonalisationRequest(RegisterCardReviewRequestDTO dto, LanguageEnum language) {

        ObjectFactory objectFactory = new ObjectFactory();
        try {
            NolCardPersonaliseApplicationRequest request = new NolCardPersonaliseApplicationRequest();
            request.setEmail(dto.getEmail());

            if (dto.getMobileNumber() != null) {
                PhoneNumber mobile = new PhoneNumber();
                mobile.setAreaCode(String.valueOf(dto.getMobileNumber().getAreaCode()));
                mobile.setCountryCode(String.valueOf(dto.getMobileNumber().getCountryCode()));
                mobile.setNumber(dto.getMobileNumber().getNumber());
                request.setMobileNumber(mobile);
            }
            if (dto.getAlternateMobileNumber() != null) {
                PhoneNumber altMobile = new PhoneNumber();
                altMobile.setAreaCode(String.valueOf(dto.getAlternateMobileNumber().getAreaCode()));
                altMobile.setCountryCode(String.valueOf(dto.getAlternateMobileNumber().getCountryCode()));
                altMobile.setNumber(dto.getAlternateMobileNumber().getNumber());
                request.setAlternateMobileNumber(altMobile);
            }
            if (dto.getFirstNameEn() != null && dto.getLastNameEn() != null) {
                FullName englishName = new FullName();
                englishName.setFirst(dto.getFirstNameAr());
                englishName.setLast(dto.getLastNameAr());
                request.setName(englishName);
            }
            if (dto.getLastNameAr() != null && dto.getLastNameAr() != null) {
                FullName arabicName = new FullName();
                arabicName.setFirst(dto.getFirstNameAr());
                arabicName.setLast(dto.getLastNameAr());
                request.setArabicName(arabicName);
            }
//            if (dto.getDocumentGroupReferenceId() != null) {
//                Document doc = new Document();
//                UploadFileReference ref = new UploadFileReference();
//                //ref.setFileReferenceNo(Long.valueOf(dto.getDocumentGroupReferenceId()));  // TODO : fileRefId from DB to be fetch via DocGrpRefId.
//                doc.setUploadedDocumentRef(ref);
//                request.setDocument(doc);
//            }
            if(dto.getDocumentNumber()!=null || dto.getDocumentType() != null || dto.getNationalityId()!= null){
                Document doc = new Document();
                doc.setIssueCountry(dto.getNationalityId());
                doc.setNumber(dto.getDocumentNumber());
                doc.setType(Objects.nonNull(dto.getDocumentType()) && dto.getDocumentType().equals(DocumentTypeEnum.EMIRATES_ID.name()) ? "N" : "P"); // TODO : enum to be added for CbtType. and make it mandatory
                if (dto.getDocumentGroupReferenceId() != null) {
//                    Document doc = new Document();
//                    UploadFileReference ref = new UploadFileReference();
                    //ref.setFileReferenceNo(Long.valueOf(dto.getDocumentGroupReferenceId()));  // TODO : fileRefId from DB to be fetch via DocGrpRefId.
//                    doc.setUploadedDocumentRef(ref);
                }
                request.setDocument(doc);
            }

            request.setMobileUserId(dto.getMobileUserId());
            request.setPaymentChannel(dto.getPaymentMeansType().getCbtType());
            request.setRequestChannel(dto.getRequestChannel().getCbtType());
            request.setSalutation(Salutation.valueOf(dto.getSalutation().name()));
            request.setTagId(Long.valueOf(dto.getNolCardId()));
            request.setDateOfBirth(toXmlDate(dto.getDateOfBirth().atStartOfDay()));
            request.setTagId(Long.valueOf(dto.getNolCardId()));
            request.setMobileUserId(dto.getMobileUserId());
            CustomHeader header = new CustomHeader();
            header.setLanguage(language.getCbtType());
            header.setUserId(dto.getIamUserId());

            JAXBElement<NolCardPersonaliseApplicationRequest> wrappedRequest =
                    objectFactory.createNolCardPersonaliseReviewRequest(request);

            WebServiceMessageCallback callback = buildCallbackWithHeader("reviewPersonaliseApplication", header);

            JAXBElement<PaymentAmountList> response = (JAXBElement<PaymentAmountList>)
                    getTemplate().marshalSendAndReceive(NOL_CARD_PERSONALIZE,wrappedRequest,callback);

            PaymentAmountList paymentAmountList = response.getValue();

            RegisterCardReviewResponseDTO pcardReviewResponseDTO = new RegisterCardReviewResponseDTO();
            //pcardReviewResponseDTO.setTotalAmount(paymentAmountList.getTotalAmount().intValue());
            if (paymentAmountList.getLineItems() != null) {
                List<LineItemResponseDTO> mappedLineItems = paymentAmountList.getLineItems().stream()
                        .map(lineItem -> {
                            LineItemResponseDTO itemDto = new LineItemResponseDTO();
                            itemDto.setName(lineItem.getName());   // assuming `LineItem` has `getName()`
                            itemDto.setValue(lineItem.getValue().toString()); // assuming `LineItem` has `getValue()`
                            return itemDto;
                        })
                        .collect(Collectors.toList());

                pcardReviewResponseDTO.setLineItems(mappedLineItems);
            }
            return pcardReviewResponseDTO;

        }
        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","reviewPersonalizeNolCard");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

    }


    public PaymentDetailsResponseDTO submitNolCardPersonalisationRequest(RegisterCardReviewRequestDTO dto, LanguageEnum language) {

        ObjectFactory objectFactory = new ObjectFactory();
        try {
            NolCardPersonaliseApplicationRequest request = new NolCardPersonaliseApplicationRequest();

            if (dto.getMobileNumber() != null) {
                PhoneNumber mobile = new PhoneNumber();
                mobile.setAreaCode(String.valueOf(dto.getMobileNumber().getAreaCode()));
                mobile.setCountryCode(String.valueOf(dto.getMobileNumber().getCountryCode()));
                mobile.setNumber(dto.getMobileNumber().getNumber());
                request.setMobileNumber(mobile);
            }
            if (dto.getAlternateMobileNumber() != null) {
                PhoneNumber altMobile = new PhoneNumber();
                altMobile.setAreaCode(String.valueOf(dto.getAlternateMobileNumber().getAreaCode()));
                altMobile.setCountryCode(String.valueOf(dto.getAlternateMobileNumber().getCountryCode()));
                altMobile.setNumber(dto.getAlternateMobileNumber().getNumber());
                request.setAlternateMobileNumber(altMobile);
            }

            if (dto.getLastNameAr() != null && dto.getLastNameAr() != null) {
                FullName arabicName = new FullName();
                arabicName.setFirst(dto.getFirstNameAr());
                arabicName.setLast(dto.getLastNameAr());
                request.setArabicName(arabicName);
            }
            if (dto.getFirstNameEn() != null && dto.getLastNameEn() != null) {
                FullName englishName = new FullName();
                englishName.setFirst(dto.getFirstNameAr());
                englishName.setLast(dto.getLastNameAr());
                request.setName(englishName);
            }
//            if (dto.getDocumentGroupReferenceId() != null) {
//                Document doc = new Document();
//                UploadFileReference ref = new UploadFileReference();
//                ref.setFileReferenceNo(Long.valueOf(dto.getDocumentGroupReferenceId()));
//                doc.setUploadedDocumentRef(ref);
//                request.setDocument(doc);
//            }
            if(dto.getDocumentNumber()!=null || dto.getDocumentType() != null || dto.getNationalityId()!= null){
                Document doc = new Document();
                doc.setIssueCountry(dto.getNationalityId());
                doc.setNumber(dto.getDocumentNumber());
                doc.setType(Objects.nonNull(dto.getDocumentType()) && dto.getDocumentType().equals(DocumentTypeEnum.EMIRATES_ID.name()) ? "N" : "P"); // TODO : enum to be added for CbtType. and make it mandatory
                if (dto.getDocumentGroupReferenceId() != null) {
//                    Document doc = new Document();
//                    UploadFileReference ref = new UploadFileReference();
                    //ref.setFileReferenceNo(Long.valueOf(dto.getDocumentGroupReferenceId()));  // TODO : fileRefId from DB to be fetch via DocGrpRefId.
//                    doc.setUploadedDocumentRef(ref);
                }
                request.setDocument(doc);
            }
            request.setTagId(Long.valueOf(dto.getNolCardId()));
            request.setPaymentChannel(dto.getPaymentMeansType().getCbtType());
            request.setSalutation(Salutation.valueOf(dto.getSalutation().name()));
            request.setEmail(dto.getEmail());
            request.setRequestChannel(dto.getRequestChannel().getCbtType());
            request.setDateOfBirth(toXmlDate(dto.getDateOfBirth().atStartOfDay()));
            request.setMobileUserId(dto.getMobileUserId());

            CustomHeader header = new CustomHeader();
            header.setLanguage(language.getCbtType());
            header.setUserId(dto.getIamUserId());

            JAXBElement<NolCardPersonaliseApplicationRequest> wrappedRequest =
                    objectFactory.createNolCardPersonaliseSubmissionRequest(request);

            WebServiceMessageCallback callback = buildCallbackWithHeader("submitPersonaliseApplicationRequest", header);

            JAXBElement<PaymentParameters> response = (JAXBElement<PaymentParameters>)
                    getTemplate().marshalSendAndReceive(NOL_CARD_PERSONALIZE,wrappedRequest,callback);
            PaymentParameters paymentParameters = response.getValue();

            PaymentDetailsResponseDTO responseDTO= new PaymentDetailsResponseDTO();
            if (paymentParameters != null && paymentParameters.getParameter() != null) {
                Map<String, String> paymentParamsMap = new HashMap<>();
                for (PaymentParameter param : paymentParameters.getParameter()) {
                    paymentParamsMap.put(param.getKey(), param.getValue());

                }
                responseDTO.setPaymentParameters(paymentParamsMap);
                Optional.ofNullable(paymentParameters.getPaymentChannel()).ifPresent(paymentChannel -> responseDTO.setPaymentMeansType(PaymentMeansTypeEnum.fromCbtType(paymentChannel)));
            }
            return responseDTO;
        }
        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","submitPersonalizeNolCard");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }


    public RegisterCardConfirmResponseDTO responseNolCardPersonalisationRequest(RegisterCardConfirmRequestDTO confirmRequestDTO, LanguageEnum language){
        ObjectFactory objectFactory = new ObjectFactory();
        try {

            PaymentParameters paymentParameters = new PaymentParameters();

            if (confirmRequestDTO.getPaymentParameters() != null) {
                for (Map.Entry<String, String> entry : confirmRequestDTO.getPaymentParameters().entrySet()) {
                    paymentParameters.getParameter().add(createParameter(entry.getKey(), entry.getValue()));
                }
            }
            paymentParameters.setPaymentChannel(confirmRequestDTO.getPaymentMeansType().getCbtType());
            paymentParameters.setRequestChannel(confirmRequestDTO.getRequestChannel().getCbtType());

            CustomHeader customHeader = new CustomHeader();
            customHeader.setLanguage(language.getCbtType());
            customHeader.setUserId(confirmRequestDTO.getIamUserId());
            JAXBElement<PaymentParameters> wrappedRequest =objectFactory.createPaymentResponse(paymentParameters);

            WebServiceMessageCallback callback = buildCallbackWithHeader("responsePersonaliseApplicationRequest", customHeader);
            JAXBElement<NolCardPersonaliseApplicationConfirmation> response = (JAXBElement<NolCardPersonaliseApplicationConfirmation>)
                    getTemplate().marshalSendAndReceive(NOL_CARD_PERSONALIZE,wrappedRequest,callback);

            NolCardPersonaliseApplicationConfirmation confirmationResponse = response.getValue();

            RegisterCardConfirmResponseDTO registerCardConfirmResponseDTO = new RegisterCardConfirmResponseDTO();
            registerCardConfirmResponseDTO.setApplicationReferenceNumber(confirmationResponse.getApplicationReferenceId());
            registerCardConfirmResponseDTO.setNolCardId(String.valueOf(confirmationResponse.getTagId()));
            registerCardConfirmResponseDTO.setConfirmationNote(confirmationResponse.getConfirmationNote());
            registerCardConfirmResponseDTO.setPaymentStatus(StringUtils.isEmpty(confirmationResponse.getStatus()) ? "FAIL" : "SUCCESS.  Application Status : " + confirmationResponse.getStatus());
            registerCardConfirmResponseDTO.setTransactionDateTime(confirmationResponse.getDate().toGregorianCalendar().toZonedDateTime().toLocalDateTime().toString());

            return registerCardConfirmResponseDTO;
        }
        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","confirmPersonalizeNolCard");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

    }

    @Override
    public void registerCardRequestValidate(RegisterCardValidateRequestDTO dto, LanguageEnum language) {

        ObjectFactory objectFactory = new ObjectFactory();
        try {
            NolCardPersonaliseApplicationRequest request = new NolCardPersonaliseApplicationRequest();

            if (dto.getMobileNumber() != null) {
                PhoneNumber mobile = new PhoneNumber();
                mobile.setAreaCode(String.valueOf(dto.getMobileNumber().getAreaCode()));
                mobile.setCountryCode(String.valueOf(dto.getMobileNumber().getCountryCode()));
                mobile.setNumber(dto.getMobileNumber().getNumber());
                request.setMobileNumber(mobile);
            }
            if (dto.getAlternateMobileNumber() != null) {
                PhoneNumber altMobile = new PhoneNumber();
                altMobile.setAreaCode(String.valueOf(dto.getAlternateMobileNumber().getAreaCode()));
                altMobile.setCountryCode(String.valueOf(dto.getAlternateMobileNumber().getCountryCode()));
                altMobile.setNumber(dto.getAlternateMobileNumber().getNumber());
                request.setAlternateMobileNumber(altMobile);
            }

            if (dto.getLastNameAr() != null && dto.getLastNameAr() != null) {
                FullName arabicName = new FullName();
                arabicName.setFirst(dto.getFirstNameAr());
                arabicName.setLast(dto.getLastNameAr());
                request.setArabicName(arabicName);
            }
            if (dto.getFirstNameEn() != null && dto.getLastNameEn() != null) {
                FullName englishName = new FullName();
                englishName.setFirst(dto.getFirstNameAr());
                englishName.setLast(dto.getLastNameAr());
                request.setName(englishName);
            }
            if(dto.getDocumentNumber()!=null || dto.getDocumentType() != null || dto.getNationalityId()!= null){
                Document doc = new Document();
                doc.setIssueCountry(dto.getNationalityId());
                doc.setNumber(dto.getDocumentNumber());
                doc.setType(Objects.nonNull(dto.getDocumentType()) && dto.getDocumentType().equals(DocumentTypeEnum.EMIRATES_ID.name()) ? "N" : "P"); // TODO : enum to be added for CbtType. and make it mandatory
                if (dto.getDocumentGroupReferenceId() != null) {
//                    Document doc = new Document();
//                    UploadFileReference ref = new UploadFileReference();
                    //ref.setFileReferenceNo(Long.valueOf(dto.getDocumentGroupReferenceId()));  // TODO : fileRefId from DB to be fetch via DocGrpRefId.
//                    doc.setUploadedDocumentRef(ref);
                }
                request.setDocument(doc);
            }
            request.setTagId(Long.valueOf(dto.getNolCardId()));
            request.setPaymentChannel(dto.getPaymentMeansType().getCbtType());
            request.setSalutation(Salutation.valueOf(dto.getSalutation().name()));
            request.setEmail(dto.getEmail());
            request.setRequestChannel(dto.getRequestChannel().getCbtType());
            request.setDateOfBirth(toXmlDate(dto.getDateOfBirth().atStartOfDay()));
            request.setMobileUserId(dto.getMobileUserId());

            CustomHeader header = new CustomHeader();
            header.setLanguage(language.getCbtType());
            header.setUserId(dto.getIamUserId());

            JAXBElement<NolCardPersonaliseApplicationRequest> wrappedRequest =
                    objectFactory.createNolCardPersonaliseValidationRequest(request);

            WebServiceMessageCallback callback = buildCallbackWithHeader("validatePersonaliseApplication", header);

            JAXBElement<BusinessValidationResult> response = (JAXBElement<BusinessValidationResult>)
                    getTemplate().marshalSendAndReceive(NOL_CARD_PERSONALIZE,wrappedRequest,callback);
             BusinessValidationResult jaxResponse =response.getValue();

             if (!jaxResponse.isSuccess()) {
                List<ValidationResponseDTO> violations = new ArrayList<>();
                if (jaxResponse.getViolations() != null) {
                    for (BusinessViolation violation : jaxResponse.getViolations()) {
                        String cbtCode = violation.getViolationCode();
                        ErrorCodesEnum matchedError = ErrorCodesEnum.fromCbtCode(cbtCode).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(matchedError.getErrorCode(), violation.getMessage()));
                    }

                }
                throw new CbtIntegrationException(null, violations , MessageKeyEnum.VALIDATION_FAILED.getCode() ,MessageKeyEnum.VALIDATION_FAILED.getMessage());
            }
        }
        catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ","validateRegisteredCard");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ",ex.getFaultCode(),ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }

    private void addCustomSoapHeader(SoapMessage soapMessage, CustomHeader header) {
        try {
            WebServiceTemplate template = getTemplate();
            Jaxb2Marshaller marshaller = (Jaxb2Marshaller) template.getMarshaller();
            marshaller.marshal(
                    new JAXBElement<>(
                            new QName(HEADER_NAMESPACE, "CustomHeader"),
                            CustomHeader.class,
                            header
                    ),
                    soapMessage.getSoapHeader().getResult()
            );
        } catch (Exception e) {
            throw new RuntimeException("Failed to add SOAP header", e);
        }
    }

    private WebServiceTemplate getTemplate() {
        return soapClientRegistry.getTemplate("nolCardPersonalize");
    }

    private PaymentParameter createParameter(String key, String value) {
        PaymentParameter paymentParameter = new PaymentParameter();
        paymentParameter.setKey(key);
        paymentParameter.setValue(value);
        return paymentParameter;
    }
    private XMLGregorianCalendar toXmlDate(LocalDateTime dateTime) {
        try {
            GregorianCalendar cal = GregorianCalendar.from(dateTime.atZone(ZoneId.systemDefault()));
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        } catch (DatatypeConfigurationException e) {
            throw new RuntimeException("Date conversion failed", e);
        }
    }
    private WebServiceMessageCallback buildCallbackWithHeader(String actionName , CustomHeader header) {
        //SoapActionCallback actionCallback = new SoapActionCallback(NOL_CARD_PERSONALIZE + actionName );
        SoapActionCallback actionCallback = new SoapActionCallback(actionName);


        Marshaller marshaller = soapClientRegistry.getTemplate(SERVICE_KEY).getMarshaller(); // Spring Marshaller

        return message -> {
            SoapMessage soapMessage = (SoapMessage) message;
            marshaller.marshal(header, soapMessage.getSoapHeader().getResult());
            actionCallback.doWithMessage(message);
        };
    }
}
