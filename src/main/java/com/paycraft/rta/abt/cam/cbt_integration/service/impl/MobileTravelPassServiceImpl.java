package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_travel_pass.*;

import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileTravelPassService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardKeyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.travelPass.*;
import com.paycraft.rta.abt.cam.common.domain.dtos.travelPass.LineItem;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.oxm.Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;

import javax.xml.datatype.XMLGregorianCalendar;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.MOBILE_TRAVEL_SERVICE;


@Service
public class MobileTravelPassServiceImpl implements MobileTravelPassService {

    private static final String SERVICE_KEY = "travelPass";

    private static final Logger log = LogManager.getLogger(MobileTravelPassServiceImpl.class);


    @Autowired
    private SoapClientRegistry soapClientRegistry;
    @Autowired
    private MobileCardEnquiryService mobileCardEnquiryService;

    private final ObjectFactory factory = new ObjectFactory();

    @SuppressWarnings("unchecked")
    public void validateRenewRequest(LanguageEnum clientLanguage,ValidateFareProductRenewalRequestDTO request) {
        try {
            TravelPassRenewRequest travelPassRenewRequest = new TravelPassRenewRequest();
            travelPassRenewRequest.setRequestChannel(request.getRequestChannel().getCbtType());
            Optional.ofNullable(request.getPaymentMeansType()).ifPresent(paymentMeansTypeEnum -> travelPassRenewRequest.setPaymentChannel(paymentMeansTypeEnum.getCbtType()));
            CardKeyDTO cardKey = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),clientLanguage);

            CardKey cardKeyRequest = new CardKey();
            cardKeyRequest.setCardGenNumber(cardKey.getCardGenNumber());
            cardKeyRequest.setCardId(cardKey.getCardId());
            travelPassRenewRequest.setCardKey(cardKeyRequest);
//            travelPassRenewRequest.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
//            travelPassRenewRequest.getCardKey().setCardId(cardKey.getCardId());
            JAXBElement<TravelPassRenewRequest> jaxbRequest = factory.createTravelPassRenewValidationRequest(travelPassRenewRequest);
            BusinessValidationResult response = ((JAXBElement<BusinessValidationResult>)
                    getTemplate().marshalSendAndReceive(MOBILE_TRAVEL_SERVICE, jaxbRequest, withSoapHeader(clientLanguage , request.getIamUserId(),"validateRenewRequest" ) ))
                    .getValue();
            log.info("Received BusinessValidationResult: {}", response);
            if (!response.isSuccess()) {
                List<ValidationResponseDTO> violations = new ArrayList<>();
                if (response.getViolations() != null) {
                    for (BusinessViolation violation : response.getViolations()) {
                        String cbtCode = violation.getViolationCode();
                        ErrorCodesEnum matchedError = ErrorCodesEnum.fromCbtCode(cbtCode).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(matchedError.getErrorCode(), violation.getMessage()));
                    }
                }
                throw new CbtIntegrationException(null, violations, MessageKeyEnum.VALIDATION_FAILED.getCode() ,MessageKeyEnum.VALIDATION_FAILED.getMessage());
            }
            log.info("validateRenewRequest completed successfully for NolCardId: {}", request.getNolCardId());

        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in validateRenewRequest(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

    }

    @SuppressWarnings("unchecked")
    public ReviewFareProductRenewalResponseDTO reviewRenewRequest(LanguageEnum clientLanguage ,ReviewFareProductRenewalRequestDTO request) {
        try {
            TravelPassRenewRequest travelPassRenewRequest = new TravelPassRenewRequest();
            travelPassRenewRequest.setPaymentChannel(request.getPaymentMeansType().getCbtType());
            travelPassRenewRequest.setRequestChannel(request.getRequestChannel().getCbtType());
            log.info("travel pass details are{}",travelPassRenewRequest);

            CardKeyDTO cardKey = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),clientLanguage);
            CardKey cardKeyRequest = new CardKey();
            cardKeyRequest.setCardGenNumber(cardKey.getCardGenNumber());
            cardKeyRequest.setCardId(cardKey.getCardId());
            travelPassRenewRequest.setCardKey(cardKeyRequest);
//            travelPassRenewRequest.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
//            travelPassRenewRequest.getCardKey().setCardId(cardKey.getCardId());

            DonationAttributes donationAttributes = new DonationAttributes();
            if (request.getAttributes() != null && !request.getAttributes().isEmpty()) {
                for (Map.Entry<String, String> entry : request.getAttributes().entrySet()) {
                    DonationAttribute donationAttribute = new DonationAttribute();
                    donationAttribute.setKey(entry.getKey());
                    donationAttribute.setValue(entry.getValue());
                    donationAttributes.getAttribute().add(donationAttribute);
                }
            }
            travelPassRenewRequest.setAttributes(donationAttributes);

            JAXBElement<TravelPassRenewRequest> jaxbRequest = factory.createTravelPassRenewReviewRequest(travelPassRenewRequest);
            PaymentAmountList paymentAmountList = ((JAXBElement<PaymentAmountList>)
                    getTemplate().marshalSendAndReceive(MOBILE_TRAVEL_SERVICE, jaxbRequest, withSoapHeader(clientLanguage,request.getIamUserId(),"reviewRenewRequest")))
                    .getValue();
            log.info("Received PaymentAmountList: {}", paymentAmountList);

            List<LineItem> lineItemsList = paymentAmountList.getLineItems()
                    .stream().map(item -> new LineItem(item.getName(), item.getValue().toString()))
                    .collect(Collectors.toList());

            ReviewFareProductRenewalResponseDTO responseDTO = new ReviewFareProductRenewalResponseDTO();
            responseDTO.setLineItems(lineItemsList);
            log.info("reviewRenewRequest() completed successfully for NolCardId: {}", request.getNolCardId());
            return  responseDTO;
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in reviewRenewRequest(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;


            if (faultInfo != null) {
                // Business Violation
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }

    @SuppressWarnings("unchecked")
    public SubmitFareProductRenewalResponseDTO submitRenewRequest(LanguageEnum clientLanguage ,SubmitFareProductRenewalRequestDTO request) {
        try {
            TravelPassRenewRequest travelPassRenewRequest = new TravelPassRenewRequest();
            travelPassRenewRequest.setPaymentChannel(request.getPaymentMeansType().getCbtType());
            travelPassRenewRequest.setRequestChannel(request.getRequestChannel().getCbtType());
            log.info("travel pass details are{}",travelPassRenewRequest);

            CardKeyDTO cardKey = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),clientLanguage);
            CardKey cardKeyRequest = new CardKey();
            cardKeyRequest.setCardGenNumber(cardKey.getCardGenNumber());
            cardKeyRequest.setCardId(cardKey.getCardId());
            travelPassRenewRequest.setCardKey(cardKeyRequest);

//            travelPassRenewRequest.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
//            travelPassRenewRequest.getCardKey().setCardId(cardKey.getCardId());

            DonationAttributes donationAttributes = new DonationAttributes();
            if (request.getAttributes() != null && !request.getAttributes().isEmpty()) {
//                for (Map.Entry<String, String> entry : request.getAttributes().entrySet()) {
//                    DonationAttribute donationAttribute = new DonationAttribute();
//                    donationAttribute.setKey(entry.getKey());
//                    donationAttribute.setValue(entry.getValue());
//                    donationAttributes.getAttribute().add(donationAttribute);
//                }
            }
            travelPassRenewRequest.setAttributes(donationAttributes);

            JAXBElement<TravelPassRenewRequest> jaxbRequest = factory.createTravelPassRenewSubmitRequest(travelPassRenewRequest);
            log.info("Created JAXB Request: {}", jaxbRequest);
            PaymentParameters paymentParameters = ((JAXBElement<PaymentParameters>)
                    getTemplate().marshalSendAndReceive(MOBILE_TRAVEL_SERVICE, jaxbRequest, withSoapHeader(clientLanguage,request.getIamUserId(),"submitRenewRequest")))
                    .getValue();
            log.info("Received PaymentParameters: {}", paymentParameters);

            SubmitFareProductRenewalResponseDTO dto = new SubmitFareProductRenewalResponseDTO();
            Optional.ofNullable(paymentParameters.getPaymentChannel()).ifPresent(paymentChannel -> dto.setPaymentMeansType(PaymentMeansTypeEnum.fromCbtType(paymentChannel)));
            // dto.setRequestChannelType(RequestChannelEnum.valueOf(paymentParameters.getRequestChannel()));
            Map<String, String> paymentParamsMap = new HashMap<>();
            if (paymentParameters.getParameter() != null) {
                for (PaymentParameter param : paymentParameters.getParameter()) {
                    paymentParamsMap.put(param.getKey(), param.getValue());
                }
            }

            dto.setPaymentParameters(paymentParamsMap);
            log.info("submitRenewRequest() completed successfully for NolCardId: {}", request.getNolCardId());
            return  dto;
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in submitRenewRequest(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;


            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

    }


    @SuppressWarnings("unchecked")
    public ConfirmFareProductRenewResponseDTO confirmRenewRequest(LanguageEnum clientLanguage ,ConfirmFareProductRenewRequestDTO request) {
        try {
            PaymentParameters paymentParameters = new PaymentParameters();
            Optional.ofNullable(request.getPaymentMeansType()).ifPresent(paymentMeansType->paymentParameters.setPaymentChannel(paymentMeansType.getCbtType()));
            paymentParameters.setRequestChannel(request.getRequestChannel().getCbtType());


            List<PaymentParameter> parameters = new ArrayList<>();
            if (request.getPaymentParameters() != null) {
                for (Map.Entry<String, String> entry : request.getPaymentParameters().entrySet()) {
                    parameters.add(createParameter(entry.getKey(), entry.getValue()));
                }
            }

            for (PaymentParameter parameter : parameters) {
                paymentParameters.getParameter().add(parameter);
            }

            JAXBElement<PaymentParameters> jaxbRequest = factory.createPaymentResponse(paymentParameters);
            log.info("Created JAXB Request: {}", jaxbRequest);
            RenewConfirmation renewConfirmation = ((JAXBElement<RenewConfirmation>)
                    getTemplate().marshalSendAndReceive(MOBILE_TRAVEL_SERVICE, jaxbRequest, withSoapHeader(clientLanguage,request.getIamUserId(),"responseRenewRequest")))
                    .getValue();
            log.info("Received RenewConfirmation: {}", renewConfirmation);
            ConfirmFareProductRenewResponseDTO dto = new ConfirmFareProductRenewResponseDTO();
            dto.setConfirmationNote(renewConfirmation.getConfirmationNote());
            dto.setTransactionReferenceId(renewConfirmation.getReferenceId());
            dto.setPaymentRefNumber(renewConfirmation.getTransactionNumber());
            dto.setProductNameEnglish(renewConfirmation.getProductName());
            dto.setProductNameArabic(null);
            dto.setPaymentStatus(renewConfirmation.getPaymentStatus().value());

            // Convert XMLGregorianCalendar to LocalDateTime
            if (renewConfirmation.getExpiryDate() != null) {
                dto.setExpiryDate(convertToLocalDateTime(renewConfirmation.getExpiryDate()));
            }

            if (renewConfirmation.getDate() != null) {
                LocalDateTime dateTime = convertToLocalDateTime(renewConfirmation.getDate());
                dto.setTransactionDateTime(dateTime.toString());
            }
            log.info("confirmRenewRequest() completed successfully for TransactionId: {}", request.getTransactionId());
            return dto;
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in confirmRenewRequest(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }

    }
    private LocalDateTime convertToLocalDateTime(XMLGregorianCalendar calendar) {
        return calendar.toGregorianCalendar()
                .toZonedDateTime()
                .toLocalDateTime();
    }

    @SuppressWarnings("unchecked")
    public TravelPassInfoResponseDTO getTravelPassInfo(LanguageEnum clientLanguage ,TravelPassInfoRequestDTO request) {
        try {
            TravelPassInfoRequest travelPassInfoRequest = new TravelPassInfoRequest();
            CardKeyDTO cardKey = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),clientLanguage);

            CardKey cardKeyRequest = new CardKey();
            cardKeyRequest.setCardGenNumber(cardKey.getCardGenNumber());
            cardKeyRequest.setCardId(cardKey.getCardId());
//            travelPassInfoRequest.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
//            travelPassInfoRequest.getCardKey().setCardId(cardKey.getCardId());
            travelPassInfoRequest.setCardKey(cardKeyRequest);

            JAXBElement<TravelPassInfoRequest> jaxbRequest = factory.createTravelPassInfoRequest(travelPassInfoRequest);
            log.info("Created JAXB Request: {}", jaxbRequest);
            TravelPassInfo soapResponse = ((JAXBElement<TravelPassInfo>)
                    getTemplate().marshalSendAndReceive(MOBILE_TRAVEL_SERVICE, jaxbRequest, withSoapHeader(clientLanguage,request.getIamUserId(),"getTravelPassInfo")))
                    .getValue();
            log.info("Received TravelPassInfo: {}", soapResponse);
            ProductInfo product = new ProductInfo();
            product.setTierId(soapResponse.getTier() != null ? soapResponse.getTier() : 0);
            product.setTierDescription(soapResponse.getTierDescription());
            product.setProductPrice(soapResponse.getFare() != null ? soapResponse.getFare().doubleValue() : 0.0);
            product.setProductDuration(soapResponse.getValidity() != null ? soapResponse.getValidity() : 0);

            if (soapResponse.getCardType() != null) {
                product.setCardType(CardTypeEnum.CBT_TYPE_TO_CARD_TYPE_MAP.get(soapResponse.getCardType().toUpperCase()));
            }

            if (soapResponse.getTravalClass() != null) {
                product.setServiceClass(ServiceClassEnum.valueOf(soapResponse.getTravalClass().toUpperCase()));
            }

            // Wrap in response DTO
            TravelPassInfoResponseDTO responseDTO = new TravelPassInfoResponseDTO();
            responseDTO.setActiveProducts(List.of(product));
            responseDTO.setExpiredProducts(List.of()); // empty list

            return responseDTO;
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in getTravelPassInfo(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;

            if (faultInfo != null&& faultInfo.getBusinessViolations() != null) {
                // Business Violations
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                // Field Validations
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations ,faultCode, faultMessage);
        }
    }

    private WebServiceTemplate getTemplate() {
        return soapClientRegistry.getTemplate(SERVICE_KEY);
    }
    private PaymentParameter createParameter(String key, String value) {
        PaymentParameter paymentParameter = new PaymentParameter();
        paymentParameter.setKey(key);
        paymentParameter.setValue(value);
        return paymentParameter;
    }

    private WebServiceMessageCallback withSoapHeader(LanguageEnum clientLanguage , String userId,String soapAction) {
        return message -> {
            try {
                SoapMessage soapMessage = (SoapMessage) message;

                CustomHeader header = new CustomHeader();
                header.setLanguage(LanguageEnum.getCbtTypeFromEnum(clientLanguage).orElse(LanguageEnum.EN.getCbtType()));
                header.setUserId(userId);

                Marshaller marshaller = soapClientRegistry.getTemplate(SERVICE_KEY).getMarshaller();
//                marshaller.marshal(
//                        new JAXBElement<>(
//                                new QName("http://www.rta.ae/ActiveMatrix/ESB/CustomHeader/XMLSchema/", "CustomHeader"),
//                                CustomHeader.class,
//                                header
//                        ),
//                        soapMessage.getSoapHeader().getResult()
//                );

                marshaller.marshal(
                        header,
                        soapMessage.getSoapHeader().getResult()
                );
                soapMessage.setSoapAction(soapAction);
            } catch (Exception e) {
                throw new RuntimeException("Failed to add SOAP header", e);
            }
        };
    }
}