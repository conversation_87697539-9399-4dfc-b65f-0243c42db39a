package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;


import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pCardApplicationRequest", propOrder = {"code"})
public class PCardApplicationStatus {

    @XmlElement(required = true)
    protected Code code;

    // Getter and Setter for 'code'
    public Code getCode() {
        return code;
    }

    public void setCode(Code value) {
        this.code = value;
    }
}