package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import com.paycraft.rta.abt.cam.common.domain.enums.SalutationEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserNameDetailsDTO extends CardRequestDTO{
    @NotNull
    private SalutationEnum salutation;
    @NotBlank
    private String nameEn;
    @NotBlank
    private String nameAr;
    @NotBlank
    private String firstNameEn;
    @NotBlank
    private String firstNameAr;
    @NotBlank
    private String lastNameEn;
    @NotBlank
    private String lastNameAr;
    private String secondNameEn;
    private String secondNameAr;
}
