package com.paycraft.rta.abt.cam.cbt_integration.controller;

import com.paycraft.rta.abt.cam.cbt_integration.service.NolPortalProfileService;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.UserProfileRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.UserProfileResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.UserProfileUpdateRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.UPDATE;
import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.USER_PROFILE;

@RestController
//@RequestMapping("/v1/card")
public class NolPortalProfileController {

    private static final Logger log = LogManager.getLogger(NolPortalProfileController.class);
    @Autowired
    NolPortalProfileService nolPortalProfileService;

    @Autowired
    private TransactionAppResponseDTOAssembler assembler;


    @PostMapping(USER_PROFILE)
    @Operation(summary = "Get Customer Profile", description = "")
    public ResponseEntity<TransactionalAppResponseDTO<UserProfileResponseDTO>> getUserProfile(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false) LanguageEnum clientLanguage, @RequestBody UserProfileRequestDTO request) {
        log.info("Request for the Get Customer Profile :{}",request.getTransactionId());
        UserProfileResponseDTO response = nolPortalProfileService.getUserProfile(request, clientLanguage);
        return ResponseEntity.ok(assembler.getResponse(response, MessageKeyEnum.SUCCESS, null));
    }

    @PostMapping(UPDATE)
    @Operation(summary = "Update Customer Profile", description = "Updates the customer profile details")
    public ResponseEntity<TransactionalAppResponseDTO<Void>> updateUserProfile(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false) LanguageEnum clientLanguage, @RequestBody UserProfileUpdateRequestDTO request) {
        log.info("Request for Update Customer Profile : {}",request.getTransactionId());
        nolPortalProfileService.updateUserProfile(request ,clientLanguage);
        return ResponseEntity.ok(assembler.getResponse(null, MessageKeyEnum.SUCCESS, null));
    }
}
