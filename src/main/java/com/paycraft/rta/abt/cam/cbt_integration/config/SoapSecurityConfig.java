package com.paycraft.rta.abt.cam.cbt_integration.config;

import org.apache.wss4j.dom.handler.WSHandlerConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.ws.soap.security.wss4j2.Wss4jSecurityInterceptor;

@Configuration
public class SoapSecurityConfig {

    @Value("${cbt.server.username}")
    private String username;

    @Value("${cbt.server.password}")
    private String password;

    @Value("${cbt.server.passwordType}")
    private String passwordType;

    @Bean
    public Wss4jSecurityInterceptor securityInterceptor() {
        Wss4jSecurityInterceptor interceptor = new Wss4jSecurityInterceptor();
        interceptor.setSecurementActions(WSHandlerConstants.USERNAME_TOKEN);
        interceptor.setSecurementUsername(username);
        interceptor.setSecurementPassword(password);
        interceptor.setSecurementPasswordType(passwordType);
        interceptor.setSecurementUsernameTokenNonce(true);
        interceptor.setSecurementUsernameTokenCreated(true);
        interceptor.setSecurementMustUnderstand(true);
        return interceptor;
    }
}