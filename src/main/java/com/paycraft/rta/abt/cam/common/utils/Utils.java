package com.paycraft.rta.abt.cam.common.utils;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Random;
import java.util.function.BiFunction;

public class Utils {

    /**
     * Generates Auth code randomly for each call.
     *
     * <p>This method generates Auth code by using 'AUTH' as prefix followed by six digit random
     * number.</p>
     *
     * @return String value with concatenation of 'AUTH' prefix and six digit random number.
     */
    public static String generateAuthCode() {
        return "AUTH" + new Random().nextInt(1000000);
    }

    public static String generateRandomCode(){
        return String.format("%06d", new Random().nextInt(1000000));
    }

    public static boolean validateNonEmvOrCardRefInput(String cardRef, String nonEmv, ConstraintValidatorContext context){

        boolean hasNonEmvTagId = (nonEmv !=null) && StringUtils.isNotBlank(nonEmv);
        boolean hasCardReferenceNumber = (cardRef !=null) && StringUtils.isNotBlank(cardRef);

        // At least one must be present
        if (!hasNonEmvTagId && !hasCardReferenceNumber) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("Either nonEmvTagId or cardReferenceNumber must be provided, but both are missing.")
                    .addConstraintViolation();
            return false;
        }
        if (hasNonEmvTagId && hasCardReferenceNumber) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("Either nonEmvTagId or cardReferenceNumber must be provided, but both are present.")
                    .addConstraintViolation();
            return false;
        }

        return true; // Valid if at least one is present
    }

    public static final BiFunction<String, String , String> getTagId = (nolTagId, isicSerialNumber) -> Objects.isNull(nolTagId) ? isicSerialNumber : nolTagId;
    public static  final BiFunction<String , String ,String > getUserId = (String iamUserId , String uaePassUserId) -> Objects.isNull(uaePassUserId) ? iamUserId: uaePassUserId;


    public static MobileNumberDTO toMobileNumberDTO(String mobileNumber) {
        String countryCode = mobileNumber.substring(0, 3);
        String areaCode = mobileNumber.substring(3, 5); // "50"
        String number = mobileNumber.substring(5);      // "1234567"
        MobileNumberDTO mobileNumberDTO = new MobileNumberDTO();
        mobileNumberDTO.setCountryCode(countryCode);
        mobileNumberDTO.setAreaCode(countryCode);
        mobileNumberDTO.setNumber(number);
        return mobileNumberDTO;
    }
}
