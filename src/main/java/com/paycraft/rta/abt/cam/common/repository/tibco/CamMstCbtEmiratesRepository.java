package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstEmiratesDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstEmiratesDTOId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CamMstCbtEmiratesRepository extends JpaRepository<CamMstEmiratesDTO, CamMstEmiratesDTOId> {

    List<CamMstEmiratesDTO> findByLanguage(String languageCode);
}
