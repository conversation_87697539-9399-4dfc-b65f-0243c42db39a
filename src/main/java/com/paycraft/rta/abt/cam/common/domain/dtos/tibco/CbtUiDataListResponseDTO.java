package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.SalutationDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.TierDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.NationalityDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.ArtWorkDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.BusinessEntitesDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.CountryDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ConcessionTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.SalutationEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.ServiceClassEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CbtUiDataListResponseDTO {
    private List<ArtWorkDTO> artWorks;
    private List<CbtEmiratesDTO> emirates;
    private List<NationalityDTO> nationalities;
    private List<CbtSalutationDTO> salutations;
    private List<String> officeAreaCodes;
    private List<String> areaCodes;
    private List<CbtServiceClassDTO> serviceClasses;
    private List<CbtConcessionTypeDTO> concessionsTypes;
    private List<CbtCountryCodesDTO> countryCodes;

    private List<BusinessEntitesDTO> businessEntities;
    private List<LanguageEnum> languages;
    private List<TierDetailsDTO> zoneTypes;
}