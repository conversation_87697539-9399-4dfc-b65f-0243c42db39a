package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtServiceClass;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtServiceClassId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CamMstCbtServiceClassRepository extends JpaRepository<CamMstCbtServiceClass, CamMstCbtServiceClassId> {
    List<CamMstCbtServiceClass> findByLanguage(String languageCode);
}
