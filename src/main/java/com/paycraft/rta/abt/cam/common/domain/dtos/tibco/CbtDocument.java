package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;


import com.paycraft.rta.abt.cam.common.domain.enums.DocumentTypeEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CbtDocument {

    private String documentGroupReferenceId;
    @NotBlank
    private DocumentTypeEnum documentType;
    @NotBlank
    private String documentFile;
    private String documentName;
    private String documentId;
}
