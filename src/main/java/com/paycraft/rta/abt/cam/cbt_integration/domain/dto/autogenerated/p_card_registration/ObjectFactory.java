//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.14 at 01:03:20 PM IST 
//


package com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration;

import javax.xml.namespace.QName;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.paycraft.rta.abt.cam.cbt_integration.xml.p_card_registration.dto package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _InitiatePCardAppPaymentResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "InitiatePCardAppPaymentResponse");
    private final static QName _PaymentAmountList_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "paymentAmountList");
    private final static QName _PCardApplicationReviewRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationReviewRequest");
    private final static QName _PCardApplicationValidationRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationValidationRequest");
    private final static QName _TrackApplicationStatusRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "trackApplicationStatusRequest");
    private final static QName _BusinessValidationResult_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "businessValidationResult");
    private final static QName _PaymentResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "paymentResponse");
    private final static QName _ResponseNolPayPCardAppPaymentResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "ResponseNolPayPCardAppPaymentResponse");
    private final static QName _PCardApplicationResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationResponse");
    private final static QName _PCardApplicationStatus_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationStatus");
    private final static QName _PCardApplicationConfirmation_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationConfirmation");
    private final static QName _PCardApplicationUIListRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationUIListRequest");
    private final static QName _ResponseNolPayPCardAppPaymentRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "responseNolPayPCardAppPaymentRequest");
    private final static QName _PCardApplicationSubmissionRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationSubmissionRequest");
    private final static QName _InitiatePCardAppPaymentRequest_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "initiatePCardAppPaymentRequest");
    private final static QName _PCardApplicationUIListResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", "pCardApplicationUIListResponse");
    private final static QName _StandardFaultException_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/StandardFaultException/XMLSchema", "StandardFaultException");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.paycraft.rta.abt.cam.cbt_integration.xml.p_card_registration.dto
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link PCardApplicationUIListResponse }
     * 
     */
    public PCardApplicationUIListResponse createPCardApplicationUIListResponse() {
        return new PCardApplicationUIListResponse();
    }

    /**
     * Create an instance of {@link PCardApplicationUIListResponse.Map }
     * 
     */
    public PCardApplicationUIListResponse.Map createPCardApplicationUIListResponseMap() {
        return new PCardApplicationUIListResponse.Map();
    }

    /**
     * Create an instance of {@link AcceptLanguages }
     * 
     */
    public AcceptLanguages createAcceptLanguages() {
        return new AcceptLanguages();
    }

    /**
     * Create an instance of {@link PaymentAmountList }
     * 
     */
    public PaymentAmountList createPaymentAmountList() {
        return new PaymentAmountList();
    }

    /**
     * Create an instance of {@link PaymentParameters }
     * 
     */
    public PaymentParameters createPaymentParameters() {
        return new PaymentParameters();
    }

    /**
     * Create an instance of {@link import LineItem }
     * 
     */
    public LineItem createLineItem() {
        return new LineItem();
    }

    /**
     * Create an instance of {@link PaymentParameter }
     * 
     */
    public PaymentParameter createPaymentParameter() {
        return new PaymentParameter();
    }

    /**
     * Create an instance of {@link InitiatePCardAppPaymentResponseVo }
     * 
     */
    public InitiatePCardAppPaymentResponseVo createInitiatePCardAppPaymentResponseVo() {
        return new InitiatePCardAppPaymentResponseVo();
    }

    /**
     * Create an instance of {@link PCardApplicationRequest }
     * 
     */
    public PCardApplicationRequest createPCardApplicationRequest() {
        return new PCardApplicationRequest();
    }

    /**
     * Create an instance of {@link TrackApplicationStatusRequest }
     * 
     */
    public TrackApplicationStatusRequest createTrackApplicationStatusRequest() {
        return new TrackApplicationStatusRequest();
    }

    /**
     * Create an instance of {@link BusinessValidationResult }
     * 
     */
    public BusinessValidationResult createBusinessValidationResult() {
        return new BusinessValidationResult();
    }

    /**
     * Create an instance of {@link ResponseNolPayPCardAppPaymentResponseVo }
     * 
     */
    public ResponseNolPayPCardAppPaymentResponseVo createResponseNolPayPCardAppPaymentResponseVo() {
        return new ResponseNolPayPCardAppPaymentResponseVo();
    }

    /**
     * Create an instance of {@link PCardApplicationResponse }
     * 
     */
    public PCardApplicationResponse createPCardApplicationResponse() {
        return new PCardApplicationResponse();
    }

    /**
     * Create an instance of {@link EnumResponse }
     * 
     */
    public EnumResponse createEnumResponse() {
        return new EnumResponse();
    }

    /**
     * Create an instance of {@link PCardApplicationConfirmation }
     * 
     */
    public PCardApplicationConfirmation createPCardApplicationConfirmation() {
        return new PCardApplicationConfirmation();
    }

    /**
     * Create an instance of {@link PCardApplicationUIListRequest }
     * 
     */
    public PCardApplicationUIListRequest createPCardApplicationUIListRequest() {
        return new PCardApplicationUIListRequest();
    }

    /**
     * Create an instance of {@link ResponseNolPayPCardAppPaymentRequestVo }
     * 
     */
    public ResponseNolPayPCardAppPaymentRequestVo createResponseNolPayPCardAppPaymentRequestVo() {
        return new ResponseNolPayPCardAppPaymentRequestVo();
    }

    /**
     * Create an instance of {@link InitiatePCardAppPaymentRequestVo }
     * 
     */
    public InitiatePCardAppPaymentRequestVo createInitiatePCardAppPaymentRequestVo() {
        return new InitiatePCardAppPaymentRequestVo();
    }

    /**
     * Create an instance of {@link LineItemVo }
     * 
     */
    public LineItemVo createLineItemVo() {
        return new LineItemVo();
    }

    /**
     * Create an instance of {@link PaymentParameterVo }
     * 
     */
    public PaymentParameterVo createPaymentParameterVo() {
        return new PaymentParameterVo();
    }

    /**
     * Create an instance of {@link PCardApplicationUIListParameter }
     * 
     */
    public PCardApplicationUIListParameter createPCardApplicationUIListParameter() {
        return new PCardApplicationUIListParameter();
    }

    /**
     * Create an instance of {@link StandardFaultInfo }
     * 
     */
    public StandardFaultInfo createStandardFaultInfo() {
        return new StandardFaultInfo();
    }

    /**
     * Create an instance of {@link BusinessViolation }
     * 
     */
    public BusinessViolation createBusinessViolation() {
        return new BusinessViolation();
    }

    /**
     * Create an instance of {@link FieldError }
     * 
     */
    public FieldError createFieldError() {
        return new FieldError();
    }

    /**
     * Create an instance of {@link IsicInfo }
     * 
     */
    public IsicInfo createIsicInfo() {
        return new IsicInfo();
    }

    /**
     * Create an instance of {@link Address }
     * 
     */
    public Address createAddress() {
        return new Address();
    }

    /**
     * Create an instance of {@link PhoneNumber }
     * 
     */
    public PhoneNumber createPhoneNumber() {
        return new PhoneNumber();
    }

    /**
     * Create an instance of {@link FullName }
     * 
     */
    public FullName createFullName() {
        return new FullName();
    }

    /**
     * Create an instance of {@link DonationAttributes }
     * 
     */
    public DonationAttributes createDonationAttributes() {
        return new DonationAttributes();
    }

    /**
     * Create an instance of {@link DonationAttribute }
     * 
     */
    public DonationAttribute createDonationAttribute() {
        return new DonationAttribute();
    }

    /**
     * Create an instance of {@link UploadFileReference }
     * 
     */
    public UploadFileReference createUploadFileReference() {
        return new UploadFileReference();
    }

    /**
     * Create an instance of {@link PCardApplicationUIListResponse.Map.Entry }
     * 
     */
    public PCardApplicationUIListResponse.Map.Entry createPCardApplicationUIListResponseMapEntry() {
        return new PCardApplicationUIListResponse.Map.Entry();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InitiatePCardAppPaymentResponseVo }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link InitiatePCardAppPaymentResponseVo }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "InitiatePCardAppPaymentResponse")
    public JAXBElement<InitiatePCardAppPaymentResponseVo> createInitiatePCardAppPaymentResponse(InitiatePCardAppPaymentResponseVo value) {
        return new JAXBElement<InitiatePCardAppPaymentResponseVo>(_InitiatePCardAppPaymentResponse_QNAME, InitiatePCardAppPaymentResponseVo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PaymentAmountList }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PaymentAmountList }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "paymentAmountList")
    public JAXBElement<PaymentAmountList> createPaymentAmountList(PaymentAmountList value) {
        return new JAXBElement<PaymentAmountList>(_PaymentAmountList_QNAME, PaymentAmountList.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationRequest }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationRequest }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationReviewRequest")
    public JAXBElement<PCardApplicationRequest> createPCardApplicationReviewRequest(PCardApplicationRequest value) {
        return new JAXBElement<PCardApplicationRequest>(_PCardApplicationReviewRequest_QNAME, PCardApplicationRequest.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationRequest }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationRequest }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationValidationRequest")
    public JAXBElement<PCardApplicationRequest> createPCardApplicationValidationRequest(PCardApplicationRequest value) {
        return new JAXBElement<PCardApplicationRequest>(_PCardApplicationValidationRequest_QNAME, PCardApplicationRequest.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TrackApplicationStatusRequest }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link TrackApplicationStatusRequest }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "trackApplicationStatusRequest")
    public JAXBElement<TrackApplicationStatusRequest> createTrackApplicationStatusRequest(TrackApplicationStatusRequest value) {
        return new JAXBElement<TrackApplicationStatusRequest>(_TrackApplicationStatusRequest_QNAME, TrackApplicationStatusRequest.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BusinessValidationResult }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link BusinessValidationResult }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "businessValidationResult")
    public JAXBElement<BusinessValidationResult> createBusinessValidationResult(BusinessValidationResult value) {
        return new JAXBElement<BusinessValidationResult>(_BusinessValidationResult_QNAME, BusinessValidationResult.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PaymentParameters }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PaymentParameters }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "paymentResponse")
    public JAXBElement<PaymentParameters> createPaymentResponse(PaymentParameters value) {
        return new JAXBElement<PaymentParameters>(_PaymentResponse_QNAME, PaymentParameters.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResponseNolPayPCardAppPaymentResponseVo }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ResponseNolPayPCardAppPaymentResponseVo }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "ResponseNolPayPCardAppPaymentResponse")
    public JAXBElement<ResponseNolPayPCardAppPaymentResponseVo> createResponseNolPayPCardAppPaymentResponse(ResponseNolPayPCardAppPaymentResponseVo value) {
        return new JAXBElement<ResponseNolPayPCardAppPaymentResponseVo>(_ResponseNolPayPCardAppPaymentResponse_QNAME, ResponseNolPayPCardAppPaymentResponseVo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationResponse")
    public JAXBElement<PCardApplicationResponse> createPCardApplicationResponse(PCardApplicationResponse value) {
        return new JAXBElement<PCardApplicationResponse>(_PCardApplicationResponse_QNAME, PCardApplicationResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnumResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link EnumResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationStatus")
    public JAXBElement<EnumResponse> createPCardApplicationStatus(EnumResponse value) {
        return new JAXBElement<EnumResponse>(_PCardApplicationStatus_QNAME, EnumResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationConfirmation }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationConfirmation }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationConfirmation")
    public JAXBElement<PCardApplicationConfirmation> createPCardApplicationConfirmation(PCardApplicationConfirmation value) {
        return new JAXBElement<PCardApplicationConfirmation>(_PCardApplicationConfirmation_QNAME, PCardApplicationConfirmation.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationUIListRequest }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationUIListRequest }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationUIListRequest")
    public JAXBElement<PCardApplicationUIListRequest> createPCardApplicationUIListRequest(PCardApplicationUIListRequest value) {
        return new JAXBElement<PCardApplicationUIListRequest>(_PCardApplicationUIListRequest_QNAME, PCardApplicationUIListRequest.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResponseNolPayPCardAppPaymentRequestVo }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ResponseNolPayPCardAppPaymentRequestVo }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "responseNolPayPCardAppPaymentRequest")
    public JAXBElement<ResponseNolPayPCardAppPaymentRequestVo> createResponseNolPayPCardAppPaymentRequest(ResponseNolPayPCardAppPaymentRequestVo value) {
        return new JAXBElement<ResponseNolPayPCardAppPaymentRequestVo>(_ResponseNolPayPCardAppPaymentRequest_QNAME, ResponseNolPayPCardAppPaymentRequestVo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationRequest }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationRequest }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationSubmissionRequest")
    public JAXBElement<PCardApplicationRequest> createPCardApplicationSubmissionRequest(PCardApplicationRequest value) {
        return new JAXBElement<PCardApplicationRequest>(_PCardApplicationSubmissionRequest_QNAME, PCardApplicationRequest.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InitiatePCardAppPaymentRequestVo }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link InitiatePCardAppPaymentRequestVo }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "initiatePCardAppPaymentRequest")
    public JAXBElement<InitiatePCardAppPaymentRequestVo> createInitiatePCardAppPaymentRequest(InitiatePCardAppPaymentRequestVo value) {
        return new JAXBElement<InitiatePCardAppPaymentRequestVo>(_InitiatePCardAppPaymentRequest_QNAME, InitiatePCardAppPaymentRequestVo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PCardApplicationUIListResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link PCardApplicationUIListResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/PCardApplicationService/XMLSchema", name = "pCardApplicationUIListResponse")
    public JAXBElement<PCardApplicationUIListResponse> createPCardApplicationUIListResponse(PCardApplicationUIListResponse value) {
        return new JAXBElement<PCardApplicationUIListResponse>(_PCardApplicationUIListResponse_QNAME, PCardApplicationUIListResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link StandardFaultInfo }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link StandardFaultInfo }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/StandardFaultException/XMLSchema", name = "StandardFaultException")
    public JAXBElement<StandardFaultInfo> createStandardFaultException(StandardFaultInfo value) {
        return new JAXBElement<StandardFaultInfo>(_StandardFaultException_QNAME, StandardFaultInfo.class, null, value);
    }

}
