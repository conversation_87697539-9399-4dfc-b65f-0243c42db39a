package com.paycraft.rta.abt.cam.cbt_integration.controller;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.ResetCardPinRequestDTO;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.ResetCardPinResponseDTO;
import com.paycraft.rta.abt.cam.cbt_integration.domain.mapper.ResetCardPinMapper;
import com.paycraft.rta.abt.cam.cbt_integration.service.ResetCardPinService;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.reset_card_pin.ResetCardPinResponse;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardmanagement.CardPinSetRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardmanagement.CardPinSetResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.RESET_PIN;


@Validated
@RestController
@Slf4j
public class ResetCardPinController {

    @Autowired
    ResetCardPinService resetCardPinService;


    @PostMapping(RESET_PIN)
    @Operation(summary = "Card Pin Set/Reset")
    public ResponseEntity<TransactionalAppResponseDTO<CardPinSetResponseDTO>> setPin(@Parameter(description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @Valid @RequestBody CardPinSetRequestDTO requestDTO) {
        log.info("Set/Reset card pin: {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(resetCardPinService.resetCardPin(requestDTO,clientLanguage));
    }

}
