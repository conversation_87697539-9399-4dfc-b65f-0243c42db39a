package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.topup;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TopupValidateSubmitRequestDTO extends CardRequestDTO {
    @NotBlank
    private String topupType;
    @NotNull
    private BigDecimal topupAmount;
    private Map<String, String> attributes;
    private PaymentMeansTypeEnum paymentMeansType;
    @Email
    private String email;
    private MobileNumberDTO mobileNumber;
}
