package com.paycraft.rta.abt.cam.common.repository.tibco;

import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtTitle;
import com.paycraft.rta.abt.cam.common.domain.entities.tibco.CamMstCbtTitleId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CamMstCbtTitleRepository extends JpaRepository<CamMstCbtTitle, CamMstCbtTitleId> {
    List<CamMstCbtTitle> findByLanguage(String languageCode);
}
