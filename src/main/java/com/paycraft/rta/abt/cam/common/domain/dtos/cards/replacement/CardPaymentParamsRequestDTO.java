package com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement;

import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.GuestUserInfo;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.RequestChannelEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardPaymentParamsRequestDTO implements Serializable {

    private long beId;
    @NotNull
    private RequestChannelEnum requestChannel;
    @NotBlank
    private String transactionId;

    private String iamUserId;

    private String uaePassUserId;

    private String abtAccountId;
    @Valid
    private GuestUserInfo guestUserInfo;
    @NotBlank
    private String referenceId;

    private PaymentMeansTypeEnum paymentMeansType;
}