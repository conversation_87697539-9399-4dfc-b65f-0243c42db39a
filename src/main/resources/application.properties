spring.application.name=cbt-integration-service
server.port=9007
spring.profiles.active=dev

#Health Management
management.endpoints.web.exposure.include=*

# Oracle DataSource Configuration
#spring.datasource.url=********************************************************
spring.datasource.url=jdbc:oracle:thin:@//${DB_URL}/${DB_NAME}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Spring Doc
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/swagger/cbt-integration-service/v3/api-docs
springdoc.swagger-ui.path=/swagger/cbt-integration-service/index.html

spring.jackson.mapper.accept-case-insensitive-enums=true
spring.jackson.deserialization.FAIL_ON_UNKNOWN_PROPERTIES=true


#soap.services.cardEnquiry=com.paycraft.rta.abt.cam.cbt_integration.xml.mobile_card_enquiry.dto

soap.services.mobileTopup=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_topup
soap.services.resetCardPin=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.reset_card_pin
soap.services.mobileResource=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_resources
soap.services.travelPass=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_travel_pass
soap.services.cardLink=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration
soap.services.nolCardEnquiry=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry
soap.services.fileUpload=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.file_upload
soap.services.personaliseCard=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nolcard_personalise
soap.services.extNolPortal=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.ext_nol_portal
soap.services.refundAdminExternal=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.refund_admin_external
soap.services.nolCardPersonalize=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nolcard_personalise
soap.services.nolC=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nolcard_personalise
soap.services.pCardRegistration=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration
soap.services.extACardRegistrationExternal=com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.ext_aCard_registration_service



cbt.proxy.host=************
cbt.proxy.port=31281
cbt.proxy.username=rta_dev
cbt.proxy.password=:$apr1$yXCWGY/l$jWXsAjHzZpdHOvn1ygvof1


cbt.server.host=***********
cbt.server.port=443
cbt.server.username=rta360abt
cbt.server.password=ABT360User@2025
cbt.server.passwordType=PasswordText



