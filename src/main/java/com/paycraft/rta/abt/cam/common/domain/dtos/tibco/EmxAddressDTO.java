package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.PointDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EmxAddressDTO {
    private String line1; // Mandatory
    private String city;  // Mandatory
    private String countryCode; // Mandatory
}
