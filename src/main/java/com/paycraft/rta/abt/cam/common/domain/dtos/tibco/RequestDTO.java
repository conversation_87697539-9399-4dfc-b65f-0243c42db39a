    package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

    import com.paycraft.rta.abt.cam.common.domain.enums.RequestChannelEnum;
    import jakarta.validation.Valid;
    import jakarta.validation.constraints.NotBlank;
    import jakarta.validation.constraints.NotNull;
    import lombok.AllArgsConstructor;
    import lombok.Data;
    import lombok.NoArgsConstructor;

    import java.io.Serializable;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public class RequestDTO implements Serializable {
        private Integer beId;
        @NotNull
        private RequestChannelEnum requestChannel;
        @NotBlank
        private String transactionId;
        private String iamUserId;
        private String uaePassUserId;
        private String abtAccountId;
        @Valid
        private GuestUserInfoDTO guestUserInfo;
    }
