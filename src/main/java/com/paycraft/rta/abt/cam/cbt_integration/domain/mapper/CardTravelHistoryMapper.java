package com.paycraft.rta.abt.cam.cbt_integration.domain.mapper;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.AbstractCardHistoryInfo;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.CardTravelHistory;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.CardTravelHistoryInfo;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.TravelHistoryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.history.TravelHistoryResponseDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mapstruct.*;

import javax.xml.datatype.XMLGregorianCalendar;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;

@Mapper(componentModel = "spring")
public interface CardTravelHistoryMapper {

    static final Logger log = LogManager.getLogger(CardTravelHistoryMapper.class);

    @Mapping(source = "historyItems", target = "history")
    TravelHistoryResponseDTO toDto(CardTravelHistory source);

    // Mapping of each history item
    @Mapping(source = "datetime", target = "transactionDateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(source = "amount", target = "amount")
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "travelFrom", ignore = true)
    @Mapping(target = "travelTo", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "beId", ignore = true)
    @Mapping(target = "transactionChannel", ignore = true)
    TravelHistoryDTO toDto(AbstractCardHistoryInfo source);

    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(XMLGregorianCalendar calendar) {
        if (calendar == null) {
            return null;
        }
        return calendar.toGregorianCalendar().toZonedDateTime().withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
    }

    @AfterMapping
    default void afterMapping(@MappingTarget TravelHistoryDTO travelHistoryDTO,  AbstractCardHistoryInfo abstractCardHistoryInfo) {
        CardTravelHistoryInfo cardTravelHistoryInfo = (CardTravelHistoryInfo) abstractCardHistoryInfo;
        travelHistoryDTO.setTravelTo(cardTravelHistoryInfo.getTravelTo());
        travelHistoryDTO.setTravelFrom(cardTravelHistoryInfo.getTravelFrom());
        travelHistoryDTO.setType(cardTravelHistoryInfo.getType());
        travelHistoryDTO.setBeId(cardTravelHistoryInfo.getBusinessEntity());
        travelHistoryDTO.setTransactionChannel(cardTravelHistoryInfo.getTransactionChannel());
    }


    //    public static TravelHistoryResponseDTO mapToResponseDTO(List<CardTravelHistoryInfo> cardHistoryList) {
//        TravelHistoryResponseDTO responseDTO = new TravelHistoryResponseDTO();
//
//        if (cardHistoryList == null || cardHistoryList.isEmpty()) {
//            responseDTO.setHistory(List.of());
//            return responseDTO;
//        }
//
//        List<TravelHistoryDTO> historyDTOList = cardHistoryList.stream()
//                .map(CardTravelHistoryMapper::mapToDTO)
//                .collect(Collectors.toList());
//
//        responseDTO.setHistory(historyDTOList);
//
//        return responseDTO;
//    }
//
//    public static TravelHistoryResponseDTO mapToResponseDTO(CardTravelHistory cardHistoryList) {
//        TravelHistoryResponseDTO responseDTO = new TravelHistoryResponseDTO();
//
//        if (cardHistoryList == null) {
//            responseDTO.setHistory(List.of());
//            return responseDTO;
//        }
//        log.info("#########################   Map to ResponseDTO for Travel History in CardTravelHistoryMapper class   ##########################");
//        List<TravelHistoryDTO> historyDTOList = cardHistoryList.stream()
//                .map(CardTravelHistoryMapper::mapToDTO)
//                .collect(Collectors.toList());
//
//        responseDTO.setHistory(historyDTOList);
//
//        return responseDTO;
//    }
//
//    private static TravelHistoryDTO mapToDTO(CardTravelHistoryInfo source) {
//        if (source == null) {
//            return null;
//        }
//
//        TravelHistoryDTO dto = new TravelHistoryDTO();
//
//        dto.setAmount(source.getAmount());
//        //dto.setTransactionDateTime(source.getDatetime().toGregorianCalendar().toZonedDateTime().withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime());
//        dto.setBeId(source.getBusinessEntity());
//        dto.setTransactionChannel(source.getTransactionChannel());
//        dto.setTravelFrom(source.getTravelFrom());
//        dto.setTravelTo(source.getTravelTo());
//        dto.setType(source.getType());
//        dto.setDescription("Travel history transaction"); // You can make this dynamic if needed
//
//        return dto;
//    }
//
// Top-level mapping from SOAP response to JSON DTO
/*    private static OffsetDateTime convertToOffsetDateTime(XMLGregorianCalendar calendar) {
        if (calendar == null) {
            return null;
        }
        return calendar.toGregorianCalendar()
                .toZonedDateTime()
                .withZoneSameInstant(ZoneOffset.UTC)
                .toOffsetDateTime();
    }*/
}
