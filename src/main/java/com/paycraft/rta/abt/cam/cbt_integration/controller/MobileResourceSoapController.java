package com.paycraft.rta.abt.cam.cbt_integration.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CbtUiDataListResponseDTO;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileResourceSoapService;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data.*;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.utils.DateTimeUtils;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.*;

@RestController
@RequestMapping
public class MobileResourceSoapController {
    
    @Autowired
    private MobileResourceSoapService service;
    
    private static final Logger log = LogManager.getLogger(MobileResourceSoapController.class);
    
    @Autowired
    private TransactionAppResponseDTOAssembler assembler;

    /**
     * Get Donation data.
     * CBT integration required
     */
    @GetMapping(DONATION_CONFIG)
    @Operation(summary = "Get Reference Data", description = "fetch other reference data")
    public ResponseEntity<TransactionalAppResponseDTO<DonationConfigResponseDTO>> getDonationData(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage) {
        log.info("Request for get Donation data : {}", DateTimeUtils.getCurrentUTCTime());
        return ResponseEntity.ok(assembler.getResponse(service.getDonationConfiguration(clientLanguage), MessageKeyEnum.SUCCESS, null));
    }

    /**
     * Get UI-Data list.
     */
    @PostMapping(UI_DATA_LIST)
    @Operation(summary = "UI-Data List", description = "To get the UI Specific Reference data list")
    public ResponseEntity<TransactionalAppResponseDTO<CbtUiDataListResponseDTO>> fetchUiDataList(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody UiDataListRequestDTO requestDTO) {
        log.info("Request for get UI Data list: {}", DateTimeUtils.getCurrentUTCTime());
        return ResponseEntity.ok(assembler.getResponse(service.getUIList(clientLanguage,requestDTO), MessageKeyEnum.SUCCESS, null));
    }

    /**
     * Image in Base64
     */
    @PostMapping(IMAGE_BASE64)
    @Operation(summary = "Image in Base64", description = "Artwork image in Base64")
    public ResponseEntity<TransactionalAppResponseDTO<ArtworkImageBase64ResponseDTO>> imageInBase54(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody ArtworkImageBase64RequestDTO requestDTO) {
        log.info("Request for Image in Base64: {}", DateTimeUtils.getCurrentUTCTime());
        return ResponseEntity.ok(assembler.getResponse(new ArtworkImageBase64ResponseDTO(service.getImageInBase64(clientLanguage,requestDTO)), MessageKeyEnum.SUCCESS, null));
    }


}
