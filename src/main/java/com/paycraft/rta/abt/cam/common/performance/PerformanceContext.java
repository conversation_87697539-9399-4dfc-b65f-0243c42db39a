package com.paycraft.rta.abt.cam.common.performance;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Thread-local context for tracking performance metrics across request lifecycle
 */
@Slf4j
public class PerformanceContext {
    
    private static final ThreadLocal<PerformanceData> CONTEXT = new ThreadLocal<>();
    
    public static void startRequest(String requestId, String apiType) {
        PerformanceData data = new PerformanceData();
        data.setRequestId(requestId);
        data.setApiType(apiType);
        data.setRequestStartTime(System.currentTimeMillis());
        CONTEXT.set(data);
        log.debug("Started performance tracking for request: {} of type: {}", requestId, apiType);
    }
    
    public static void startExternalCall(String externalService, String operation) {
        PerformanceData data = CONTEXT.get();
        if (data != null) {
            ExternalCallMetric metric = new ExternalCallMetric();
            metric.setServiceName(externalService);
            metric.setOperation(operation);
            metric.setStartTime(System.currentTimeMillis());
            data.getCurrentExternalCalls().put(getCallKey(externalService, operation), metric);
            log.debug("Started external call tracking: {} - {}", externalService, operation);
        }
    }
    
    public static void endExternalCall(String externalService, String operation) {
        PerformanceData data = CONTEXT.get();
        if (data != null) {
            String key = getCallKey(externalService, operation);
            ExternalCallMetric metric = data.getCurrentExternalCalls().remove(key);
            if (metric != null) {
                metric.setEndTime(System.currentTimeMillis());
                metric.setDurationMs(metric.getEndTime() - metric.getStartTime());
                data.getCompletedExternalCalls().add(metric);
                log.debug("Completed external call: {} - {} in {}ms", 
                    externalService, operation, metric.getDurationMs());
            }
        }
    }
    
    public static void endRequest() {
        PerformanceData data = CONTEXT.get();
        if (data != null) {
            data.setRequestEndTime(System.currentTimeMillis());
            data.setTotalResponseTimeMs(data.getRequestEndTime() - data.getRequestStartTime());
            
            // Calculate external API time
            long totalExternalTime = data.getCompletedExternalCalls().stream()
                .mapToLong(ExternalCallMetric::getDurationMs)
                .sum();
            data.setTotalExternalApiTimeMs(totalExternalTime);
            
            // Calculate internal processing time
            data.setInternalProcessingTimeMs(data.getTotalResponseTimeMs() - totalExternalTime);
            
            log.info("Request {} completed - Total: {}ms, External: {}ms, Internal: {}ms", 
                data.getRequestId(), 
                data.getTotalResponseTimeMs(),
                data.getTotalExternalApiTimeMs(),
                data.getInternalProcessingTimeMs());
        }
    }
    
    public static PerformanceData getCurrentData() {
        return CONTEXT.get();
    }
    
    public static void clear() {
        CONTEXT.remove();
    }
    
    private static String getCallKey(String service, String operation) {
        return service + ":" + operation;
    }
    
    @Data
    public static class PerformanceData {
        private String requestId;
        private String apiType;
        private long requestStartTime;
        private long requestEndTime;
        private long totalResponseTimeMs;
        private long totalExternalApiTimeMs;
        private long internalProcessingTimeMs;
        
        private final ConcurrentMap<String, ExternalCallMetric> currentExternalCalls = new ConcurrentHashMap<>();
        private final List<ExternalCallMetric> completedExternalCalls = new ArrayList<>();
    }
    
    @Data
    public static class ExternalCallMetric {
        private String serviceName;
        private String operation;
        private long startTime;
        private long endTime;
        private long durationMs;
        private boolean success = true;
        private String errorMessage;
    }
}
