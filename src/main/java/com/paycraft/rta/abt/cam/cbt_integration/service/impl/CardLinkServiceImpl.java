package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.ErrorResponse;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.ext_nol_portal.SubmitExtCardRemovalRequest;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.ext_nol_portal.SubmitExtCardRemovalResponse;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.BusinessValidationResult;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.BusinessViolation;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.CardKey;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.CardRegistrationRequest;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.CustomHeader;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.FieldError;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.ObjectFactory;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.RegistrationConfirmation;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_card_registration.StandardFaultInfo;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.CardLinkService;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cardLinkDelink.CardDeLinkRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cardLinkDelink.CardLinkRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cardLinkDelink.CardLinkResponseDataDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardKeyDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.oxm.Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.CARD_LINK_SERVICE;
import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.NOL_PORTAL_PROFILE;

@Service
public class CardLinkServiceImpl implements CardLinkService {

    private static final Logger log = LogManager.getLogger(CardLinkServiceImpl.class);
    private static final String SERVICE_KEY = "cardLink";

    @Autowired
    private MobileCardEnquiryService mobileCardEnquiryService;

    @Autowired
    private SoapClientRegistry soapClientRegistry;

    @Autowired
    private TransactionAppResponseDTOAssembler assembler;

    private final ObjectFactory factory = new ObjectFactory();

    private static final Map<String, ErrorResponse> errorMap = Map.of(
            "0", new ErrorResponse("Success", "200"),
            "1", new ErrorResponse("Default card Error", "3070"),
            "2", new ErrorResponse("Card does not exist", "3067"),
            "3", new ErrorResponse("User does not exist", "3071"),
            "9", new ErrorResponse("System Error", "3069")
    );

    private WebServiceMessageCallback withSoapHeader(LanguageEnum clientLanguage, String userId, String soapAction) {
        return message -> {
            try {
                SoapMessage soapMessage = (SoapMessage) message;
                CustomHeader header = new CustomHeader();
                header.setLanguage(LanguageEnum.getCbtTypeFromEnum(clientLanguage).orElse(LanguageEnum.EN.getCbtType()));
                header.setUserId(userId);
                soapMessage.setSoapAction(soapAction);

                Marshaller marshaller = soapClientRegistry.getTemplate(SERVICE_KEY).getMarshaller();
                marshaller.marshal(header, soapMessage.getSoapHeader().getResult());
            } catch (Exception e) {
                throw new RuntimeException("Failed to add SOAP header", e);
            }
        };
    }

    @Override
    @Transactional
    public CardLinkResponseDataDTO linkCard(LanguageEnum clientLanguage, CardLinkRequestDTO requestDto) {
        CardRegistrationRequest soapRequest = new CardRegistrationRequest();
        CardKeyDTO cardKeyDTO = mobileCardEnquiryService.findCardKey(requestDto.getNolCardId(), requestDto.getIamUserId(), clientLanguage);
        CardKey cardKey = new CardKey();
        cardKey.setCardGenNumber(cardKeyDTO.getCardGenNumber());
        cardKey.setCardId(cardKeyDTO.getCardId());
        soapRequest.setCardKey(cardKey);
        soapRequest.setPin(requestDto.getPin());

        JAXBElement<CardRegistrationRequest> jaxbRequest = factory.createCardRegistrationSubmitRequest(soapRequest);
        log.info("Prepared SOAP Request for linkCard: {}", soapRequest);
        try {
            WebServiceTemplate template = soapClientRegistry.getTemplate(SERVICE_KEY);

            @SuppressWarnings("unchecked")
            RegistrationConfirmation response = ((JAXBElement<RegistrationConfirmation>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(CARD_LINK_SERVICE, jaxbRequest, withSoapHeader(clientLanguage, requestDto.getIamUserId(), "submitRegistration")))
                    .getValue();
            log.info("Received SOAP response :{}", response);
            CardLinkResponseDataDTO responseDto = new CardLinkResponseDataDTO();
            responseDto.setConfirmationNote(response.getConfirmationNote());
            responseDto.setMessages(response.getFeatureNotes());
            log.info("linkCard successful for NolCardId: {}", requestDto.getNolCardId());
            return responseDto;

        } catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in linkCard(). FaultCode: {}, FaultMessage: {}", ex.getFaultCode(), ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }
    }

    @Override
    @Transactional
    public void validateLinkCard(LanguageEnum clientLanguage, CardLinkRequestDTO requestDTO) {
        CardRegistrationRequest soapRequest = new CardRegistrationRequest();
        CardKeyDTO cardKeyDTO = mobileCardEnquiryService.findCardKey(requestDTO.getNolCardId(), requestDTO.getIamUserId(), clientLanguage);
        CardKey cardKey = new CardKey();
        cardKey.setCardGenNumber(cardKeyDTO.getCardGenNumber());
        cardKey.setCardId(cardKeyDTO.getCardId());
        soapRequest.setCardKey(cardKey);
        soapRequest.setPin(requestDTO.getPin());

        JAXBElement<CardRegistrationRequest> jaxbRequest = factory.createCardRegistrationValidationRequest(soapRequest);
        log.info("Prepared SOAP Request for validateLinkCard: {}", soapRequest);
        try {
            WebServiceTemplate template = soapClientRegistry.getTemplate(SERVICE_KEY);
            @SuppressWarnings("unchecked")
            BusinessValidationResult response = ((JAXBElement<BusinessValidationResult>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(CARD_LINK_SERVICE, jaxbRequest, withSoapHeader(clientLanguage, requestDTO.getIamUserId(), "validateRegistration")))
                    .getValue();
            log.info("Received SOAP response :{}", response);
            if (!response.isSuccess()) {
                List<ValidationResponseDTO> violations = new ArrayList<>();
                if (response.getViolations() != null) {
                    for (BusinessViolation violation : response.getViolations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), violation.getMessage() + " : " + violation.getViolationCode()));
                    }
                }
                log.info("Validation failed for NolCardId: {}, Violations: {}", requestDTO.getNolCardId(), violations);
                throw new CbtIntegrationException(null, violations, null, null);
            }
            return;
        } catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in validateLinkCard(). FaultCode: {}, FaultMessage: {}", ex.getFaultCode(), ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }
    }

    @Override
    public TransactionalAppResponseDTO deLinkCard(LanguageEnum clientLanguage, CardDeLinkRequestDTO request) {

        SubmitExtCardRemovalRequest jaxRequest = new SubmitExtCardRemovalRequest();
        CardKeyDTO key = mobileCardEnquiryService.findCardKey(request.getNolCardId(), request.getIamUserId(), clientLanguage);
        jaxRequest.setCardGenNo(key.getCardGenNumber());
        jaxRequest.setCardId(key.getCardId());
        jaxRequest.setUserId(request.getIamUserId());
        log.info("Prepared SOAP Request for deLinkCard: {}", jaxRequest);

        JAXBElement<SubmitExtCardRemovalRequest> wrappedRequest = new JAXBElement<>(
                new QName("http://cardmasterenquiry.service.rta.cbo.octopuscards.com/extNolPortal", "submitExtCardRemovalRequest"),
                SubmitExtCardRemovalRequest.class,
                jaxRequest
        );

        try {
            WebServiceTemplate template = soapClientRegistry.getTemplate("extNolPortal");
            @SuppressWarnings("unchecked")
            SubmitExtCardRemovalResponse response = ((JAXBElement<SubmitExtCardRemovalResponse>) template.marshalSendAndReceive(NOL_PORTAL_PROFILE, wrappedRequest, withSoapAction("submitExtCardRemovalRequest")))
                    .getValue();
            log.info("Received response from CBT for deLinkCard: {}", response.getResponseCode());
            log.info("Error Map: {}", errorMap);
            ErrorResponse errorResponse = errorMap.get(String.valueOf(response.getResponseCode()));
            return assembler.getResponse(null, errorResponse.getInternalCode(), errorResponse.getMessage(), request.getTransactionId());


        } catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException occurred in deLinkCard(). FaultCode: {}, FaultMessage: {}", ex.getFaultCode(), ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }
    }

    private WebServiceMessageCallback withSoapAction(String soapAction) {
        return message -> {
            ((SoapMessage) message).setSoapAction(soapAction);
        };
    }
}
