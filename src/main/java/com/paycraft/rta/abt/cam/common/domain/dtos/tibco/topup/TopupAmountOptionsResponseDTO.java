package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.topup;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.OptionsDTO;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TopupAmountOptionsResponseDTO implements Serializable {
    private BigDecimal balance;
    private BigDecimal debt;
    private BigDecimal minTopUp;
    private BigDecimal maxTopUp;
    private List<BigDecimal> options;
}
