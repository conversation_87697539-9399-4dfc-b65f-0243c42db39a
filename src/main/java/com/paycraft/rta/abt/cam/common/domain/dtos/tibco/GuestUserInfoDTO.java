package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.MobileNumber;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class GuestUserInfoDTO {
    private MobileNumber mobileNumber;
    @Email
    private String email;
}
