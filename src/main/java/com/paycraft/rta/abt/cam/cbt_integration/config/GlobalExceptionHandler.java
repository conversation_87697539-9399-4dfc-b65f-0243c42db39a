package com.paycraft.rta.abt.cam.cbt_integration.config;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.exception.ResourceNotFoundException;
import com.paycraft.rta.abt.cam.common.exception.SoapHeaderException;
import com.paycraft.rta.abt.cam.common.exception.SoapMarshallingException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.springframework.ws.client.WebServiceIOException;

import java.net.ConnectException;
import java.util.List;

import static com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum.INVALID_REQUEST;

@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    private static final Logger log = LogManager.getLogger(GlobalExceptionHandler.class);


    // Handles custom business exception
    @ExceptionHandler(CbtIntegrationException.class)
    public ResponseEntity<AppResponseDTO> handleIntegrationException(CbtIntegrationException ex) {
        log.error("CbtIntegrationException: {}", ex.getResponseMessage());
        AppResponseDTO response = new AppResponseDTO(ex.getResponseCode(), ex.getResponseMessage() ,ex.getViolations(),null);
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // Handles resource not found
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<AppResponseDTO> handleNotFound(ResourceNotFoundException ex) {
        log.error("ResourceNotFoundException: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(AppResponseDTO.of(
                        MessageKeyEnum.RESOURCE_NOT_FOUND.getCode(),
                        MessageKeyEnum.RESOURCE_NOT_FOUND.getMessage(),
                        List.of(ValidationResponseDTO.of(ErrorCodesEnum.RESOURCE_NOT_FOUND.getErrorCode(), ex.getMessage()))
                ));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<AppResponseDTO> handleGenericException(Exception ex, HttpServletRequest request) {
        log.error("Unhandled exception at [{}]: {}", request.getRequestURI(), ex.getMessage(), ex);
        AppResponseDTO response = AppResponseDTO.of(
                MessageKeyEnum.SERVICE_CALL_FAILED.getCode(),
                MessageKeyEnum.SERVICE_CALL_FAILED.getMessage(),
                List.of(ValidationResponseDTO.of(ErrorCodesEnum.INTERNAL_ERROR.getErrorCode(), "An unexpected error occurred"))
        );
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }



    // Handles NullPointerException
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<AppResponseDTO> handleNullPointer(NullPointerException ex) {
        log.error("NullPointerException: {}", ex.getMessage(), ex);
        String message = ex.getMessage() != null ? ex.getMessage() : "Null pointer exception occurred";
        return ResponseEntity.badRequest()
                .body(AppResponseDTO.of(
                        MessageKeyEnum.VALIDATION_FAILED.getCode(),
                        MessageKeyEnum.VALIDATION_FAILED.getMessage(),
                        List.of(ValidationResponseDTO.of(ErrorCodesEnum.DATABASE_ERROR.getErrorCode(), message))
                ));
    }

    // Handles malformed JSON or datatype mismatch in body
    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(
            org.springframework.http.converter.HttpMessageNotReadableException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        log.error("HttpMessageNotReadableException: {}", ex.getMessage(), ex);
        String message = "Malformed request or invalid data type";

        if (ex.getCause() instanceof InvalidFormatException formatEx) {
            String field = formatEx.getPath().stream()
                    .map(ref -> ref.getFieldName())
                    .findFirst().orElse("unknown");
            String expectedType = formatEx.getTargetType().getSimpleName();
            message = "Invalid value for field '" + field + "', expected type: " + expectedType;
        }

        AppResponseDTO response = AppResponseDTO.of(
                INVALID_REQUEST.getCode(),
                INVALID_REQUEST.getMessage(),
                List.of(ValidationResponseDTO.of(ErrorCodesEnum.INVALID_REQUEST.getErrorCode(), message))
        );

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    // Handles path/query param type mismatch
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<AppResponseDTO> handleTypeMismatch(MethodArgumentTypeMismatchException ex) {
        log.error("MethodArgumentTypeMismatchException: {}", ex.getMessage(), ex);
        String message = "Invalid value for parameter '" + ex.getName() +
                "', expected type: " + (ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "unknown");

        AppResponseDTO response = AppResponseDTO.of(
                INVALID_REQUEST.getCode(),
                INVALID_REQUEST.getMessage(),
                List.of(ValidationResponseDTO.of(ErrorCodesEnum.INVALID_REQUEST.getErrorCode(), message))
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(WebServiceIOException.class)
    public ResponseEntity<AppResponseDTO> handleWebServiceIOException(WebServiceIOException ex) {
        log.error("WebServiceIOException: {}", ex.getMessage(), ex);

        Throwable cause = ex.getRootCause();
        String message;

        if (cause instanceof ConnectException) {
            message = "Failed to connect to external service: " + cause.getMessage();
        } else {
            message = "I/O error during web service call: " + ex.getMessage();
        }

        AppResponseDTO response = AppResponseDTO.of(
                MessageKeyEnum.SERVICE_CALL_FAILED.getCode(),
                MessageKeyEnum.SERVICE_CALL_FAILED.getMessage(),
                List.of(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), message))
        );

        return new ResponseEntity<>(response, HttpStatus.SERVICE_UNAVAILABLE);
    }

    @ExceptionHandler(SoapHeaderException.class)
    public ResponseEntity<String> handleSoapHeaderException(SoapHeaderException ex) {
        String errorMessage = "An error occurred while setting the SOAP header: " + ex.getMessage();
        return new ResponseEntity<>(errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(SoapMarshallingException.class)
    public ResponseEntity<String> handleMarshallingException(SoapMarshallingException ex) {
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Technical error occurred while preparing the request. Please try again later.");
    }


}
