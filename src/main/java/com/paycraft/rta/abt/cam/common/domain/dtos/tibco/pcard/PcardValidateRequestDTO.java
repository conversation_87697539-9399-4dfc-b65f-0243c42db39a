package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ConcessionTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.ServiceClassEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class PcardValidateRequestDTO extends RequestDTO {

    @NotBlank
    private String nameEn;
    @NotBlank
    private String nameAr;
    @NotBlank
    private String firstNameEn;
    @NotBlank
    private String firstNameAr;
    @NotBlank
    private String lastNameEn;
    @NotBlank
    private String lastNameAr;
    @NotBlank
    private String secondNameEn;
    @NotBlank
    private String secondNameAr;
    @Valid
    @NotNull
    private MobileNumberDTO mobileNumber;
    @Valid
    private MobileNumberDTO alternateMobileNumber;

    private AddressDTO address;
    @NotNull
    @Email
    private String email;
    private String photo;
    @NotBlank
    private String photoReferenceId;
    @NotNull
    private ServiceClassEnum serviceClass;
    @NotNull
    private Integer artWorkId;
    @NotNull
    private ConcessionTypeEnum concessionType;
    @NotNull
    private Integer nationalityId;
    private String mobileUserId;
    @NotNull
    private LocalDate dateOfBirth;

    private Long emiratesId;
    private String passportNo;
    @NotNull
    private YesOrNoEnum onBehalfFlag;
    @NotNull
    private YesOrNoEnum isicEnabled;

    private String isicSchoolShortName;
    private OnBehalfDetailsRequestDTO onBehalfDetails;
}
