package com.paycraft.rta.abt.cam.common.repository;

import com.paycraft.rta.abt.cam.common.domain.entities.CamDocumentDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CamDocumentDetailsRepository extends JpaRepository<CamDocumentDetails, Long> {
    @Procedure(name = "get_next_dgrid_id")
    String getNextDgridId();  // for OUT parameter only

    @Query(value = "SELECT fnc_gen_document_group_ref_id FROM dual", nativeQuery = true)
    String generateDocumentRefNo();

    List<CamDocumentDetails> findByDocumentGroupReferenceId(String documentGroupRefId);
}


