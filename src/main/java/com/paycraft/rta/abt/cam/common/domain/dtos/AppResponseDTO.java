package com.paycraft.rta.abt.cam.common.domain.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter
public class AppResponseDTO<T> implements Serializable {
    public String responseCode;
    public String responseMessage;
    public List<ValidationResponseDTO> errors;
    public T data;

    private AppResponseDTO(String responseCode, MessageKeyEnum responseMessage, List<ValidationResponseDTO> errors, T data) {
        this.responseCode = responseCode;
        this.responseMessage = responseMessage.getMessage();
        this.errors = errors;
        this.data = data;
    }

    public AppResponseDTO(String responseCode, String responseMessage, List<ValidationResponseDTO> errors, T data) {
        this.responseCode = responseCode;
        this.responseMessage = responseMessage;
        this.errors = errors;
        this.data = data;
    }


    public static <T> AppResponseDTO<T> of(String code, MessageKeyEnum message, List<ValidationResponseDTO> o, T t) {
        return new AppResponseDTO<>(code, message, o, t);
    }

    public static <T> AppResponseDTO<T> of(String code, String message, List<ValidationResponseDTO> o, T t) {
        return new AppResponseDTO<>(code, message, o, t);
    }

    public static <T> AppResponseDTO<T> of(String code, String message, List<ValidationResponseDTO> o) {
        return new AppResponseDTO<>(code, message, o, null);
    }

    public static <T> AppResponseDTO of(Object o, String code, String message, List<ValidationResponseDTO> errors, Object o1, Object o2) {
        return new AppResponseDTO<>(code, message, errors, null);
    }

    public static <T> AppResponseDTO of(MessageKeyEnum messageKey, T data) {
        return new AppResponseDTO<>(messageKey.getCode(), messageKey.getMessage(), null, data);
    }
}