package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UiDataListRequestDTO {
    private YesOrNoEnum specialLayoutRequired;
    private YesOrNoEnum areaCodeRequired;
    private YesOrNoEnum officeAreaCodeRequired;
    private YesOrNoEnum nationalityRequired;
    private YesOrNoEnum concessionTypeRequired;
    private YesOrNoEnum serviceClassRequired;
    private YesOrNoEnum titleRequired;
    private YesOrNoEnum emirateRequired;

    private YesOrNoEnum businessEntityRequired;
    private YesOrNoE<PERSON> languageRequired;
    private YesOrNoEnum zoneTypeRequired;
    private YesOrNoEnum countryCodeRequired;
}
