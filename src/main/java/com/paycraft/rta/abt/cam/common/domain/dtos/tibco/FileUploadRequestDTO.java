package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileUploadRequestDTO extends RequestDTO {
    @NotNull
    @Valid
    private List<CbtDocument> documents;
}
