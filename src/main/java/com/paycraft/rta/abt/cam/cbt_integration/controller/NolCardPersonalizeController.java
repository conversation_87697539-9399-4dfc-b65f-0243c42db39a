package com.paycraft.rta.abt.cam.cbt_integration.controller;

import com.paycraft.rta.abt.cam.cbt_integration.service.NolCardPersonalizeService;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.LineItemResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.PcardSubmitResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.register_card.*;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.EndpointConstants.*;

@Validated
@RestController
public class NolCardPersonalizeController {

    @Autowired
    NolCardPersonalizeService nolCardPersonalizeService;
    private static final Logger log = LogManager.getLogger(NolCardPersonalizeController.class);
    @Autowired
    private TransactionAppResponseDTOAssembler assembler;

    @PostMapping(CARD_REGISTER_REQUEST_REVIEW)
    @Operation(summary = "Register Card Request Review", description = "Validate Card Personalize registration")
    public ResponseEntity<TransactionalAppResponseDTO<List<LineItemResponseDTO>>> registerCardRequestReview(@Parameter(description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody RegisterCardReviewRequestDTO requestDTO) {
        log.info("Request for Register Card Request : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(nolCardPersonalizeService.reviewNolcardPersonalisationRequest(requestDTO,clientLanguage).getLineItems(),MessageKeyEnum.SUCCESS,requestDTO.getTransactionId()));
    }

    /**
     *
     */
    @PostMapping(CARD_REGISTER_REQUEST_SUBMIT)
    @Operation(summary = "Register Card Request Submit", description = "Validate Card Personalize registration")
    public ResponseEntity<TransactionalAppResponseDTO<PcardSubmitResponseDTO>> registerCardRequestSubmit(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody RegisterCardReviewRequestDTO requestDTO) {
        log.info("Request for Register Card Request : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(nolCardPersonalizeService.submitNolCardPersonalisationRequest(requestDTO,clientLanguage),MessageKeyEnum.SUCCESS, requestDTO.getTransactionId()));
    }

    /**
     *
     */
    @PostMapping(CARD_REGISTER_REQUEST_CONFIRM)
    @Operation(summary = "Register Card Request Confirm", description = "Validate Card Personalize registration")
    public ResponseEntity<TransactionalAppResponseDTO<RegisterCardConfirmResponseDTO>> registerCardRequestConfirm(@Parameter (description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody RegisterCardConfirmRequestDTO requestDTO) {
        log.info("Request for Register Card Request : {}", requestDTO.getTransactionId());
        return ResponseEntity.ok(assembler.getResponse(nolCardPersonalizeService.responseNolCardPersonalisationRequest(requestDTO,clientLanguage),MessageKeyEnum.SUCCESS,requestDTO.getTransactionId()));
    }

    @PostMapping(CARD_REGISTER_REQUEST_VALIDATE)
    @Operation(summary = "Register Card Request Validate", description = "Validate Card Personalize registration")
    public ResponseEntity<TransactionalAppResponseDTO> registerCardRequestValidate(@Parameter(description = "Global language header") @RequestHeader(name = "X-CLIENT-LANGUAGE", required = true) LanguageEnum clientLanguage, @RequestBody RegisterCardValidateRequestDTO requestDTO) {
        log.info("Request for Register Card Request : {}", requestDTO.getTransactionId());
        nolCardPersonalizeService.registerCardRequestValidate(requestDTO,clientLanguage);
        return ResponseEntity.ok(assembler.getResponse(null, MessageKeyEnum.SUCCESS, requestDTO.getTransactionId()));
    }



}
