package com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.Address;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.GuestUserInfo;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.MobileNumber;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubmitCardReplacementRequestDTO {

    private long beId;
    @NotNull
    private RequestChannelEnum requestChannel;
    @NotBlank
    private String transactionId;
    private String iamUserId;
    private String uaePassUserId;
    private String abtAccountId;
    @Valid
    private GuestUserInfo guestUserInfo;
    @NotBlank
    private String nolCardId;
    private String cardToken;
    @NotNull
    private SalutationEnum salutation;
    @NotBlank
    private String nameEn;
    @NotBlank
    private String nameAr;
    @NotBlank
    private String firstNameEn;
    @NotBlank
    private String firstNameAr;
    @NotBlank
    private String lastNameEn;
    @NotBlank
    private String lastNameAr;
    @NotBlank
    private String secondNameEn;
    @NotBlank
    private String secondNameAr;
    @Valid
    @NotBlank
    private MobileNumberDTO mobileNumber;
    @Valid
    private MobileNumberDTO alternateMobileNumber;
    @NotBlank
    @Email
    private String email;
    @Valid
    private ReplacementAddress address;
    @NotBlank
    private String documentGroupReferenceId;
    private String photo;
    @NotBlank
    private String photoReferenceId;
    @NotNull
    private ServiceClassEnum serviceClass;
    @NotNull
    private Integer artWorkId;
    @NotNull
    private ConcessionTypeEnum concessionType;
    @NotNull
    private Integer nationalityId;
    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;
    private Map<String,String> attributes;
    private YesOrNoEnum identityCheckPassed;
    private YesOrNoEnum onBehalfCheckPassed;
    private YesOrNoEnum concessionCheckPassed;
    private YesOrNoEnum isicCheckPassed;
    @NotNull
    private FareMediaFormFactorEnum fareMediaFormFactor;
    @NotBlank
    private String mobileUserId;
    private PaymentMeansTypeEnum paymentMeansType;
    @NotBlank
    private String pin;
    @NotBlank
    private String reason;
    @NotBlank
    private String autoValidationReferenceNumber;
    @NotBlank
    private String referenceId;
    @Schema(hidden = true)
    private String documentNumber;
    @Schema(hidden = true)
    private DocumentTypeEnum documentType;
}
