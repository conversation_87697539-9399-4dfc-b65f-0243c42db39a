package com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.NationalityResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonDetailsResponseDto {
    private String transactionId;
    private PersonInfo personInfo;
    private List<PassportDetail> passportDetails;
    private ImmigrationFile immigrationFile;
    private ContactInfo contactInfo;
    private Address address;
    private NationalityResponseDTO prsNationality;
    private Profession profession;
    private SponsorDetails sponsorDetails;
    private String photo;

    // Get<PERSON> and Setters
}

