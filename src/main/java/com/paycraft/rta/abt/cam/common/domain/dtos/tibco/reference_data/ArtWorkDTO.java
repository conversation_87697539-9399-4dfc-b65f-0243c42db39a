package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.reference_data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ArtWorkDTO {
    private String artWorkId;
    private String artWorkNameEn;
    private String artWorkNameAr;
    private String artWorkShortNameEn;
    private String artWorkShortNameAr;
    private String categoryId;
    private String typeId;
    private BigDecimal artWorkFee;
    private LocalDateTime lastUpdateDateTime;
}
