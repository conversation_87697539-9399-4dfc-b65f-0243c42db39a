package com.paycraft.rta.abt.cam.common.domain.dtos.tibco;

import com.paycraft.rta.abt.cam.common.domain.enums.AccountActionTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.CommunicationModeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.RequestChannelEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SendOtpRequestDTO {

    private Integer beId;
    @NotNull
    private RequestChannelEnum requestChannel;
    @NotBlank
    private String transactionId;
    @NotNull
    private CommunicationModeEnum media;
    @Valid
    private MobileNumberDTO mobileNumber;
    @Email
    private String email;
    @NotNull
    private String actionType;

}
