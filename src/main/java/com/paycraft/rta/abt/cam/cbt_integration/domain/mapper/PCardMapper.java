package com.paycraft.rta.abt.cam.cbt_integration.domain.mapper;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.p_card_registration.*;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.AddressDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.PcardConfirmRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.PcardReviewSubmitRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.DocumentTypeEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import javax.xml.datatype.XMLGregorianCalendar;
import java.time.LocalDate;

@Mapper(componentModel = "spring",uses = PCardMappingUtil.class, imports = {LocalDate.class, XMLGregorianCalendar.class , YesOrNoEnum.class, DocumentType.class, DocumentTypeEnum.class})
public interface PCardMapper {


    @Mapping(target = "dateOfBirth", source = "dateOfBirth", qualifiedByName = "localDateToXmlGregorian")
    @Mapping(target = "address", source = "address")
    @Mapping(target = "address.flat", source = "address.houseNo")
    @Mapping(target = "alternateMobileNumber", source = "alternateMobileNumber")
    @Mapping(target = "mobileNumber", source = "mobileNumber")
    @Mapping(target = "arabicName", source = ".", qualifiedByName = "mapArabicName")
    @Mapping(target = "name", source = ".", qualifiedByName = "mapEnglishName")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "mobileUserId", source = "mobileUserId")
    @Mapping(target = "nationalityId", source = "nationalityId")
    @Mapping(target = "salutation", source = "salutation", qualifiedByName = "mapSalutation")
    @Mapping(target = "passengerClass", source = "serviceClass", qualifiedByName = "mapPassengerClass")
    @Mapping(target = "concessionType", source = "concessionType", qualifiedByName = "mapConcessionType")
    @Mapping(target = "identityCheckPassed", expression = "java(toYesNoString(pcardReviewSubmitRequestDTO.getIdentityCheckPassed()))")
    @Mapping(target = "onBehalfCheckPassed", expression = "java(toYesNoString(pcardReviewSubmitRequestDTO.getOnBehalfCheckPassed()))")
    @Mapping(target = "isicEnabled", expression = "java(pcardReviewSubmitRequestDTO.getIsicCheckPassed() != null && pcardReviewSubmitRequestDTO.getIsicCheckPassed() == YesOrNoEnum.YES ? 1 : 0)")
    @Mapping(target = "requestChannel", source = "requestChannel", qualifiedByName = "mapRequestChannel")
    @Mapping(target = "cardMedia", source = "fareMediaFormFactor", qualifiedByName = "mapCardMedia")
    @Mapping(target = "attributes", source = "attributes", qualifiedByName = "mapAttributes")
    @Mapping(target = "photo", source = "photoReferenceId", qualifiedByName = "mapPhotoReference")
    @Mapping(target = "documentType",expression ="java(DocumentType.valueOf(pcardReviewSubmitRequestDTO.getDocumentType().getCbtType()))")
    PCardApplicationRequest toSoapRequest(PcardReviewSubmitRequestDTO pcardReviewSubmitRequestDTO);


    PhoneNumber map(MobileNumberDTO dto);

    Address map(AddressDTO dto);


    default String toYesNoString(YesOrNoEnum value) {
        if (value == null) return null;
        return value == YesOrNoEnum.YES ? "Y" : "N";
    }

    PCardApplicationConfirmation toSoapRequest(PcardConfirmRequestDTO pcardConfirmRequestDTO);
}
