package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.mobile_topup.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileNolCardTopupService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardKeyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.topup.*;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.PaymentMeansTypeEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.namespace.QName;
import java.util.*;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.MOBILE_CARD_TOPUP_SERVICE;


@Service
public class MobileNolCardTopupServiceImpl implements MobileNolCardTopupService {
    private static final String SERVICE_KEY = "mobileTopup";
    @Autowired
    private SoapClientRegistry soapClientRegistry;
    private static final Logger log = LogManager.getLogger(MobileResourceSoapServiceImpl.class);

    @Autowired
    MobileCardEnquiryService mobileCardEnquiryService;

    private final ObjectFactory factory = new ObjectFactory();

    private WebServiceMessageCallback withHeader(LanguageEnum langEnum, String soapAction) {
        return message -> {
            try {
                SoapMessage soapMessage = (SoapMessage) message;
                soapMessage.setSoapAction(soapAction);

                AcceptLanguages header = new AcceptLanguages();
                header.setLanguage(LanguageEnum.getCbtTypeFromEnum(langEnum).orElse(LanguageEnum.EN.getCbtType()));

                JAXBElement<AcceptLanguages> headerElement = new JAXBElement<>(
                        new QName("http://www.rta.ae/ActiveMatrix/ESB/AcceptLanguage/XMLSchema/", "AcceptLanguages"),
                        AcceptLanguages.class,
                        header
                );

                soapClientRegistry.getTemplate(SERVICE_KEY)
                        .getMarshaller()
                        .marshal(headerElement, soapMessage.getSoapHeader().getResult());

            } catch (Exception e) {
                throw new RuntimeException("Failed to add SOAP header", e);
            }
        };
    }


    public void validateOnlineTopup(TopupValidateSubmitRequestDTO request , LanguageEnum language) {
        try{
            NolCardTopupRequest nolCardTopupValidationRequest = new NolCardTopupRequest();

            //fetch from getCardKey
            CardKeyDTO cardKeyResponse = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),language);
            CardKey cardKey = new CardKey();
            cardKey.setCardId(cardKeyResponse.getCardId());
            cardKey.setCardGenNumber(cardKeyResponse.getCardGenNumber());

//            nolCardTopupValidationRequest.getCardKey().setCardId(cardKey.getCardId());
//            nolCardTopupValidationRequest.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
            nolCardTopupValidationRequest.setCardKey(cardKey);
            nolCardTopupValidationRequest.setEmail(request.getEmail());
            Optional.ofNullable(request.getMobileNumber()).ifPresent(mobileNumberDTO -> nolCardTopupValidationRequest.setMobileNumber(mobileNumberDTO.getCountryCode()+mobileNumberDTO.getAreaCode()+mobileNumberDTO.getNumber()));
            nolCardTopupValidationRequest.setRequestChannel(request.getRequestChannel().getCbtType());
            nolCardTopupValidationRequest.setPaymentChannel(request.getPaymentMeansType().getCbtType());
            nolCardTopupValidationRequest.setTopupAmount(request.getTopupAmount());
            nolCardTopupValidationRequest.setTopupType(TopupType.valueOf(request.getTopupType()));

            JAXBElement<NolCardTopupRequest> jaxbRequest = factory.createNolCardTopupValidationRequest(nolCardTopupValidationRequest);
            log.info("Created JAXB Request: {}", jaxbRequest);
            @SuppressWarnings("unchecked")
            BusinessValidationResult response = ((JAXBElement<BusinessValidationResult>)soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(MOBILE_CARD_TOPUP_SERVICE, jaxbRequest, withHeader(language , "validateOnlineTopup"))).getValue();
            log.info("Received BusinessValidationResult from SOAP: {}", response);
            if (!response.isSuccess()) {
                List<ValidationResponseDTO> violations = new ArrayList<>();
                if (response.getViolations() != null) {
                    for (BusinessViolation violation : response.getViolations()) {
                        String cbtCode = violation.getViolationCode();
                        ErrorCodesEnum matchedError = ErrorCodesEnum.fromCbtCode(cbtCode).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(matchedError.getErrorCode(), violation.getMessage()));
                    }

                }
                throw new CbtIntegrationException(null, violations , MessageKeyEnum.VALIDATION_FAILED.getCode() ,MessageKeyEnum.VALIDATION_FAILED.getMessage());
            }
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in validateOnlineTopup(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }

    public TopupSubmitResponseDTO submitTopupRequest(TopupValidateSubmitRequestDTO request , LanguageEnum language) {
        try {
            NolCardTopupRequest nolCardTopupSubmitRequest = new NolCardTopupRequest();

            CardKeyDTO cardKeyResponse = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),language);
            CardKey cardKey = new CardKey();
            cardKey.setCardId(cardKeyResponse.getCardId());
            cardKey.setCardGenNumber(cardKeyResponse.getCardGenNumber());
            nolCardTopupSubmitRequest.setCardKey(cardKey);
//            nolCardTopupSubmitRequest.getCardKey().setCardId(cardKey.getCardId());
//            nolCardTopupSubmitRequest.getCardKey().setCardGenNumber(cardKey.getCardGenNumber());
            if(request.getEmail()!=null)nolCardTopupSubmitRequest.setEmail(request.getEmail());
            if(request.getMobileNumber()!=null)nolCardTopupSubmitRequest.setMobileNumber(request.getMobileNumber().getCountryCode()+request.getMobileNumber().getAreaCode()+request.getMobileNumber().getNumber());
            if(request.getRequestChannel()!=null)nolCardTopupSubmitRequest.setRequestChannel(request.getRequestChannel().getCbtType());
            if(request.getPaymentMeansType()!=null)nolCardTopupSubmitRequest.setPaymentChannel(request.getPaymentMeansType().getCbtType());
            if(request.getTopupAmount()!=null) nolCardTopupSubmitRequest.setTopupAmount(request.getTopupAmount());
            if(request.getTopupType()!=null)nolCardTopupSubmitRequest.setTopupType(TopupType.valueOf(request.getTopupType()));

            JAXBElement<NolCardTopupRequest> jaxbRequest = factory.createNolCardTopupSubmissionRequest(nolCardTopupSubmitRequest);
            log.info("Created JAXB Request: {}", jaxbRequest);
            @SuppressWarnings("unchecked")
            PaymentParameters response = (( JAXBElement< PaymentParameters>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(MOBILE_CARD_TOPUP_SERVICE, jaxbRequest, withHeader(language , "submitTopupRequest"
            ))).getValue();
            log.info("Received PaymentParameters from SOAP: {}", response);
            TopupSubmitResponseDTO responseDTO = new TopupSubmitResponseDTO();
            if (response != null && response.getParameter() != null) {
                Map<String, String> paymentParamsMap = new HashMap<>();
                for (PaymentParameter param : response.getParameter()) {
                    paymentParamsMap.put(param.getKey(), param.getValue());

                }
                responseDTO.setPaymentParameters(paymentParamsMap);
            }

            responseDTO.setPaymentMeansType(Objects.nonNull(response) && Objects.nonNull(response.getPaymentChannel()) ? PaymentMeansTypeEnum.fromCbtType(response.getPaymentChannel()) : request.getPaymentMeansType());

            return responseDTO;
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in submitTopupRequest(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null &&
                        faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code)
                                .orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }

    public TopupConfirmResponseDTO responseTopupRequest(TopupConfirmRequestDTO request , LanguageEnum language) {

        try {
            PaymentParameters paymentParameters = new PaymentParameters();
            paymentParameters.setPaymentChannel(request.getPaymentMeansType().getCbtType());
            paymentParameters.setRequestChannel(request.getRequestChannel().getCbtType());

            if (request.getPaymentParameters() != null) {
                for (Map.Entry<String, String> entry : request.getPaymentParameters().entrySet()) {
                    paymentParameters.getParameter().add(createParameter(entry.getKey(), entry.getValue()));
                }
            }

            JAXBElement<PaymentParameters> jaxbRequest = factory.createPaymentResponse(paymentParameters);
            log.info("Created JAXB Request: {}", jaxbRequest);
            @SuppressWarnings("unchecked")
            TopupConfirmation response = ((JAXBElement<TopupConfirmation>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(MOBILE_CARD_TOPUP_SERVICE, jaxbRequest, withHeader(language, "responseTopupRequest"
            ))).getValue();
            log.info("Received TopupConfirmation from SOAP: {}", response);
            TopupConfirmResponseDTO topupConfirmResponseDTO = new TopupConfirmResponseDTO();
            if(response.getActiveNote()!=null)topupConfirmResponseDTO.setActiveNote(response.getActiveNote());
            if(response.getConfirmationNote()!=null)topupConfirmResponseDTO.setConfirmationNote(response.getConfirmationNote());
            if(response.getDate()!=null)topupConfirmResponseDTO.setTransactionDateTime(response.getDate() != null ? response.getDate().toGregorianCalendar().toZonedDateTime().toLocalDateTime() : null);
            if(response.getPaymentStatus()!=null)topupConfirmResponseDTO.setPaymentStatus(response.getPaymentStatus() != null ? response.getPaymentStatus().value() : null);
            if(response.getTagId()!=null)topupConfirmResponseDTO.setNolCardId(response.getTagId());
            if(response.getTransactionNumber()!=null) topupConfirmResponseDTO.setTransactionReferenceNumber(response.getTransactionNumber());
            if (response.getTopupBalanceChange() != null) {
                topupConfirmResponseDTO.setLastBalance(response.getTopupBalanceChange().getPreviousBalance());
                topupConfirmResponseDTO.setTopupAmount(response.getTopupBalanceChange().getTopupAmount());
                topupConfirmResponseDTO.setCurrentBalance(response.getTopupBalanceChange().getTotalBalance());
            }
            return topupConfirmResponseDTO;
        }
        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in responseTopupRequest(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }

    public TopupAmountOptionsResponseDTO getTopupAmountOptions(CardRequestDTO request , LanguageEnum language) {

        try {

            CardKeyDTO cardKeyDTO = mobileCardEnquiryService.findCardKey(request.getNolCardId(),request.getIamUserId(),language);
            CardKey cardKey = new CardKey();
            cardKey.setCardId(cardKeyDTO.getCardId());
            cardKey.setCardGenNumber(cardKeyDTO.getCardGenNumber());
            JAXBElement<CardKey> jaxbRequest = factory.createCardKey(cardKey);
            log.info("Created JAXB Request: {}", jaxbRequest);
            @SuppressWarnings("unchecked")
            TopupAmountOptions response = ((JAXBElement<TopupAmountOptions>) soapClientRegistry.getTemplate(SERVICE_KEY).
                    marshalSendAndReceive(MOBILE_CARD_TOPUP_SERVICE, jaxbRequest, withHeader(language, "getTopUpAmountOptions"

            ))).getValue();
            log.info("Received TopupAmountOptions from SOAP: {}", response);
            TopupAmountOptionsResponseDTO topupAmountOptionsResponseDTO = new TopupAmountOptionsResponseDTO();
            topupAmountOptionsResponseDTO.setMaxTopUp(response.getMaxAmountAllowed());
            topupAmountOptionsResponseDTO.setOptions(response.getOptions());

            return topupAmountOptionsResponseDTO;
        }

        catch (SoapFaultClientException ex) {
            log.error("SoapFaultClientException in getTopupAmountOptions(): {}", ex.getMessage(), ex);
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()
                        ));
                    }
                }
            }

            throw new CbtIntegrationException(exptionRefrenceId , violations , faultCode, faultMessage);
        }
    }

    private PaymentParameter createParameter(String key, String value) {
        PaymentParameter paymentParameter = new PaymentParameter();
        paymentParameter.setKey(key);
        paymentParameter.setValue(value);
        return paymentParameter;
    }
}
