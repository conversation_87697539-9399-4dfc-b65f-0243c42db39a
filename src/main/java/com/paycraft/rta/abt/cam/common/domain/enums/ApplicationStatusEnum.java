package com.paycraft.rta.abt.cam.common.domain.enums;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.ReferenceDetails;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ApplicationStatusEnum {
    IN_PROGRESS("1", "Work is ongoing", "PROCESSING", "In Progress", "قيد التقدم"),
    CONFIRMED("8", "Application Registration Confirmed", null, "Confirmed", "تم التأكيد"),
    READY_FOR_MANUFACTURING("2", "Preparation is complete, awaiting manufacturing", null, "Ready for Manufacturing", "جاهز للتصنيع"),
    MANUFACTURING("3", "Currently in production", null, "Manufacturing", "قيد التصنيع"),
    READY_FOR_DISPATCH("4", "Manufacturing is complete, awaiting shipment", null, "Ready for Dispatch", "جاهز للإرسال"),
    DISPATCHED("5", "Dispatched", "DISPATCHED", "Dispatched", "تم الإرسال"),
    DELIVERED("6", "Successfully delivered", null, "Delivered", "تم التسليم"),
    IN_REVIEW("7", "Application is under Review", null, "In Review", "قيد المراجعة"),
    REJECTED("9", "Application is Rejected", "REJECTED", "Rejected", "مرفوض"),
    ON_HOLD("13", "Application on hold", "HOLD", "On Hold", "قيد الانتظار"),
    APPROVED("10", "Approved", "APPROVED", "Approved", "تمت الموافقة"),
    CLOSED("11", "Closed", "CLOSED", "Closed", "مغلق"),
    PAYMENT("12", "Payment", "PAYMENT", "Payment", "دفع");

    private final String id;
    private final String description;
    private final String cbtType;
    private final String nameEn;
    private final String nameAr;

    public static final Map<String, ApplicationStatusEnum> ID_TO_ENUM_MAP = Stream.of(ApplicationStatusEnum.values()).collect(Collectors.toMap(ApplicationStatusEnum::getId, status -> status));

    public static final Map<String, ApplicationStatusEnum> CBT_TYPE_TO_STATUS_MAP =
            Stream.of(ApplicationStatusEnum.values())
                    .filter(status -> status.getCbtType() != null) // Avoid null cbtType
                    .collect(Collectors.toMap(ApplicationStatusEnum::getCbtType, status -> status));

    public static List<ReferenceDetails> getReferenceDetails() {
        return Stream.of(ApplicationStatusEnum.values())
                .filter(applicationStatusEnum -> Objects.nonNull(applicationStatusEnum.getCbtType()))
                .map(reason -> new ReferenceDetails(
                        reason.getCbtType(),
                        reason.getNameEn(),
                        reason.getNameAr()
                ))
                .collect(Collectors.toList());
    }


}