package com.paycraft.rta.abt.cam.common.domain.enums;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.ReferenceDetails;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum CardTypeEnum {

    UNDEFINED("0", "Undefined", null, "UNKNOWN_CARD_TYPE", "Undefined", "غير معرف"),
    PERSONALIZED_CARD("1", "Personalized Card", null, "PERSONALIZED_CARD", "Personalized", "مُخصصة"),
    ANONYMOUS_CARD("2", "Anonymous Card", null, "ANONYMOUS_CARD", "Anonymous", "مجهولة"),
    CONTACTLESS_TICKET_OR_2D_BARCODE("3", "Contactless Ticket/2D Barcode", null, "CASUAL_TICKET", "Contactless Ticket / Barcode", "تذكرة بدون لمس / باركود"),
    REGISTERED_ANONYMOUS_CARD("4", "Registered Anonymous Card", null, null, "Registered Anonymous", "مجهولة مسجلة");

    private final String cardTypeId;
    private final String cardType;
    private final String cmsType;
    private final String cbtType;
    private final String nameEn;
    private final String nameAr;

    public static final Map<String, CardTypeEnum> ID_TO_CARD_TYPE_MAP = Stream.of(CardTypeEnum.values())
            .collect(Collectors.toMap(CardTypeEnum::getCardTypeId, type -> type));

    public static List<ReferenceDetails> getReferenceDetails() {
        return Stream.of(CardTypeEnum.values())
                .filter(type -> Objects.nonNull(type.getCbtType()))
                .map(type -> new ReferenceDetails(
                        type.name(),         // ID from enum name
                        type.getNameEn(),    // English name
                        type.getNameAr()     // Arabic name
                ))
                .collect(Collectors.toList());
    }

    public static final Map<String, CardTypeEnum> CBT_TYPE_TO_CARD_TYPE_MAP = Stream.of(CardTypeEnum.values())
            .collect(Collectors.toMap(CardTypeEnum::getCbtType, type -> type));

}
