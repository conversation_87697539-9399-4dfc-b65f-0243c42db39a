package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.ErrorResponse;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.BusinessViolation;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.FieldError;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.StandardFaultInfo;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.reset_card_pin.ObjectFactory;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.reset_card_pin.ResetCardPinRequest;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.reset_card_pin.ResetCardPinResponse;

import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.ResetCardPinService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.assembler.TransactionAppResponseDTOAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionalAppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.MobileNumberDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardmanagement.CardPinSetRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.cardmanagement.CardPinSetResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.RESET_CARD_PIN;

@Service
public class ResetCardPinServiceImpl implements ResetCardPinService {
    private static final Logger log = LogManager.getLogger(ResetCardPinServiceImpl.class);
    private static final String SERVICE_KEY = "resetCardPin";

    private static final Map<String, ErrorResponse> errorMap = Map.of(
            "0", new ErrorResponse("Success", "200"),
            "1", new ErrorResponse("Incorrect Customer Information", "3064"),
            "2", new ErrorResponse("Invalid Card Type", "3065"),
            "3", new ErrorResponse("Card is not enabled", "3066"),
            "4", new ErrorResponse("Card does not exist", "3067"),
            "9", new ErrorResponse("Invalid Schema", "3068"),
            "99", new ErrorResponse("System Error", "3069")
    );

    @Autowired
    SoapClientRegistry soapClientRegistry;

    @Autowired
    private TransactionAppResponseDTOAssembler<CardPinSetResponseDTO> assembler;

    public TransactionalAppResponseDTO<CardPinSetResponseDTO> resetCardPin(CardPinSetRequestDTO requestDTO, LanguageEnum languageEnum) {
        // 1. Build request
        ObjectFactory factory = new ObjectFactory();
        ResetCardPinRequest request = new ResetCardPinRequest();
        request.setCardTagId(requestDTO.getNolCardId());
        Optional.ofNullable(requestDTO.getConfirmReset()).ifPresent(advanceMode -> request.setConfirmReset(requestDTO.getConfirmReset().getCbtType()));
        OffsetDateTime dob = requestDTO.getDateOfBirth().atStartOfDay(ZoneId.of("Asia/Dubai")).toOffsetDateTime();
        request.setDateOfBirth(convertToXmlGregorianCalendar(dob));

        JAXBElement<ResetCardPinRequest> wrappedRequest = factory.createResetCardPinRequest(request);
        JAXBElement<ResetCardPinResponse> response = null;
        try {
            response = (JAXBElement<ResetCardPinResponse>)
                    getTemplate().marshalSendAndReceive(
                            RESET_CARD_PIN,
                            wrappedRequest,
                            new SoapActionCallback("resetCardPin")
                    );
        } catch (SoapFaultClientException ex) {
            log.info("API Hitted : {} ", "card-info");
            log.error("Soap Exception Occured -> Fault Code : {}  & Fault Message :{} ", ex.getFaultCode(), ex.getMessage());
            String faultCode = CbtMessageKeyEnum.fromCbtCode(ex.getFaultCode().toString()).map(cbtEnum -> cbtEnum.getErrorCode().toString()).orElse(MessageKeyEnum.VALIDATION_FAILED.getCode());
            String faultMessage = ex.getFaultStringOrReason();
            List<ValidationResponseDTO> violations = new ArrayList<>();
            StandardFaultInfo faultInfo = SoapFaultParserUtil.parseFaultDetailV1(ex, StandardFaultInfo.class);
            String exptionRefrenceId = null;
            if (faultInfo != null) {
                exptionRefrenceId = faultInfo.getExceptionReferenceId();
                if (faultInfo.getBusinessViolations() != null && faultInfo.getBusinessViolations().getViolations() != null) {

                    for (BusinessViolation violation : faultInfo.getBusinessViolations().getViolations()) {
                        String code = violation.getViolationCode();
                        String msg = violation.getMessage();
                        ErrorCodesEnum errorCode = ErrorCodesEnum.fromCbtCode(code).orElse(ErrorCodesEnum.SERVICE_CALL_ERROR);
                        violations.add(ValidationResponseDTO.of(errorCode.getErrorCode(), msg));
                    }
                }
                if (faultInfo.getFieldValidations() != null) {
                    for (FieldError fieldError : faultInfo.getFieldValidations()) {
                        violations.add(ValidationResponseDTO.of(ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(), fieldError.getMessage() + ": " + fieldError.getField()));
                    }
                }
            }
            throw new CbtIntegrationException(exptionRefrenceId, violations, faultCode, faultMessage);
        }

        ResetCardPinResponse resetCardPinResponse = response.getValue();
        CardPinSetResponseDTO cardPinSetResponseDTO = new CardPinSetResponseDTO();
        cardPinSetResponseDTO.setEmail(resetCardPinResponse.getEmail());
        String fullNumber = resetCardPinResponse.getMobileNumber();
        if (fullNumber.length() == 12) {
            MobileNumberDTO mobileNumberDTO = new MobileNumberDTO(
                    fullNumber.substring(0, 3),   // countryCode
                    fullNumber.substring(3, 5),   // areaCode
                    fullNumber.substring(5)       // number
            );
            cardPinSetResponseDTO.setMobileNumber(mobileNumberDTO);
        }

        ErrorResponse errorResponse = errorMap.get(resetCardPinResponse.getStatus());
        return assembler.getResponse(cardPinSetResponseDTO, errorResponse.getInternalCode(),errorResponse.getMessage(),requestDTO.getTransactionId());
    }

    public XMLGregorianCalendar convertToXmlGregorianCalendar(OffsetDateTime offsetDateTime) {
        GregorianCalendar calendar = GregorianCalendar.from(offsetDateTime.toZonedDateTime());
        try {
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(calendar);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert OffsetDateTime to XMLGregorianCalendar", e);
        }
    }

    private WebServiceTemplate getTemplate() {
        return soapClientRegistry.getTemplate(SERVICE_KEY);
    }
}

