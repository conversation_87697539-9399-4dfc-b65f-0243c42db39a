package com.paycraft.rta.abt.cam.common.domain.dtos.Profile;


import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.RequestChannelEnum;
import jakarta.validation.constraints.Email;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserProfileUpdateRequestDTO implements Serializable {

    private long beId;

    private RequestChannelEnum requestChannel;

    private String transactionId;

    private String iamUserId;

    private String uaePassUserId;

    private String abtAccountId;

    private GuestUserInfo guestUserInfo;

    private Address address;

    private String nolCardId;
    @Email
    private String email;

    private LanguageEnum preferredLanguage;

    private MobileNumber mobileNumber;

    private Map<String, String> otpIdentifier;
}