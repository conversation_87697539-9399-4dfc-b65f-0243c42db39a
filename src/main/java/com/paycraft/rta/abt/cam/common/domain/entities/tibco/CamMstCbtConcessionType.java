package com.paycraft.rta.abt.cam.common.domain.entities.tibco;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "CAM_MST_CBT_CONCESSION_TYPE")
@IdClass(CamMstCbtConcessionTypeId.class)
public class CamMstCbtConcessionType {

    @Id
    @Column(name = "ID")
    private String id; // enum constant

    @Column(name = "TYPE_EN")
    private String typeEn;

    @Column(name = "TYPE_AR")
    private String typeAr;

    @Id
    private String language;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "CREATED_DATE")
    private LocalDateTime createdDate;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "UPDATED_DATE")
    private LocalDateTime updatedDate;
}
