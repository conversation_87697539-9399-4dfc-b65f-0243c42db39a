package com.paycraft.rta.abt.cam.cbt_integration.service.impl;

import com.paycraft.rta.abt.cam.cbt_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.ext_nol_portal.*;
import com.paycraft.rta.abt.cam.cbt_integration.domain.enums.CbtMessageKeyEnum;
import com.paycraft.rta.abt.cam.cbt_integration.service.MobileCardEnquiryService;
import com.paycraft.rta.abt.cam.cbt_integration.service.NolPortalProfileService;
import com.paycraft.rta.abt.cam.cbt_integration.util.SoapFaultParserUtil;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.*;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CardKeyDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.domain.dtos.Profile.UserProfileResponseDTO;
import jakarta.xml.bind.JAXBElement;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ws.soap.client.SoapFaultClientException;

import java.util.ArrayList;
import java.util.List;

import static com.paycraft.rta.abt.cam.cbt_integration.domain.constants.SoapUrlConstant.NOL_PORTAL_PROFILE;

@Service
@RequiredArgsConstructor
public class NolPortalProfileServiceImpl implements NolPortalProfileService {
    private static final Logger log = LogManager.getLogger(NolPortalProfileServiceImpl.class);

    @Autowired
    private MobileCardEnquiryService mobileCardEnquiryService;

    private final SoapClientRegistry soapClientRegistry;
    private final ObjectFactory factory = new ObjectFactory();
    private static final String SERVICE_KEY = "extNolPortal";

    @Override
    public UserProfileResponseDTO getUserProfile(UserProfileRequestDTO requestDTO, LanguageEnum languageEnum) {
        try {
            CardKeyDTO cardKeyDTO = mobileCardEnquiryService.findCardKey(requestDTO.getNolCardId(), requestDTO.getIamUserId(), languageEnum);
            GetNolCustomerProfileRequest soapRequest = new GetNolCustomerProfileRequest();
            soapRequest.setCardGenNo(cardKeyDTO.getCardGenNumber());
           soapRequest.setCardId(Long.parseLong(requestDTO.getNolCardId()));
            JAXBElement<GetNolCustomerProfileRequest> jaxbRequest = factory.createGetNolCustomerProfileRequest(soapRequest);
            log.info("Prepared SOAP Request :{} ",jaxbRequest);
            GetNolCustomerProfileResponse soapResponse = ((JAXBElement<GetNolCustomerProfileResponse>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(NOL_PORTAL_PROFILE, jaxbRequest)).getValue();
            log.info("Received SOAP response for getUserProfile: {}", soapResponse);
            UserProfileResponseDTO responseDTO = new UserProfileResponseDTO();
            responseDTO.setNolCardId(requestDTO.getNolCardId());
            if (soapResponse != null) {
                if (soapResponse.getEmail() != null) responseDTO.setEmail(soapResponse.getEmail());
                if (soapResponse.getPrimaryPhone() != null) responseDTO.setMobileNumber(new MobileNumber(soapResponse.getPrimaryPhone().getPrefix(), soapResponse.getPrimaryPhone().getAreaCode(), soapResponse.getPrimaryPhone().getPhoneNumber()));
                if (soapResponse.getLanguage() != null) responseDTO.setPreferredLanguage(LanguageEnum.valueOf(soapResponse.getLanguage().toUpperCase()));
                if (soapResponse.getAddress() != null) responseDTO.setAddress(new Address(soapResponse.getAddress().getAddressLine1(), soapResponse.getAddress().getAddressLine2(), soapResponse.getAddress().getAddressLine3(), soapResponse.getAddress().getAddressLine4(), soapResponse.getAddress().getAddressLine5(), soapResponse.getAddress().getAddressLine6(), soapResponse.getAddress().getAddressLine7()));
            }
            log.info("getUserProfile successful for NolCardId: {}", requestDTO.getNolCardId());
            return responseDTO;
        } catch (Exception ex) {
            throw new CbtIntegrationException(null, null, MessageKeyEnum.SERVICE_CALL_FAILED.getCode(), "Failed to fetch customer profile: " + ex.getMessage());
        }
    }

    @Override
    public void updateUserProfile(UserProfileUpdateRequestDTO requestDTO, LanguageEnum languageEnum) {
        try {
            UpdateNolCustomerProfileRequest soapRequest = new UpdateNolCustomerProfileRequest();
            CardKeyDTO cardKeyDTO = mobileCardEnquiryService.findCardKey(requestDTO.getNolCardId(), requestDTO.getIamUserId(), languageEnum);
            soapRequest.setCardGenNo(cardKeyDTO.getCardGenNumber());
           soapRequest.setCardId(Long.parseLong(requestDTO.getNolCardId()));
            if (requestDTO.getEmail() != null) soapRequest.setEmail(requestDTO.getEmail());
            if (requestDTO.getPreferredLanguage() != null) soapRequest.setLanguage(requestDTO.getPreferredLanguage().name());
            if (requestDTO.getMobileNumber() != null) {
                GetNolCustomerMobilePhone phone = new GetNolCustomerMobilePhone();
                if (requestDTO.getMobileNumber().getCountryCode() != null) phone.setPrefix(requestDTO.getMobileNumber().getCountryCode());
                if (requestDTO.getMobileNumber().getAreaCode() != null) phone.setAreaCode(requestDTO.getMobileNumber().getAreaCode());
                if (requestDTO.getMobileNumber().getNumber() != null) phone.setPhoneNumber(requestDTO.getMobileNumber().getNumber());
                soapRequest.setPrimaryPhone(phone);
            }
            if (requestDTO.getAddress() != null) {
                GetNolCustomerAddress address = new GetNolCustomerAddress();
                if (requestDTO.getAddress().getAddressLine1() != null) address.setAddressLine1(requestDTO.getAddress().getAddressLine1());
                if (requestDTO.getAddress().getAddressLine2() != null) address.setAddressLine2(requestDTO.getAddress().getAddressLine2());
                if (requestDTO.getAddress().getAddressLine3() != null) address.setAddressLine3(requestDTO.getAddress().getAddressLine3());
                if (requestDTO.getAddress().getAddressLine4() != null) address.setAddressLine4(requestDTO.getAddress().getAddressLine4());
                if (requestDTO.getAddress().getAddressLine5() != null) address.setAddressLine5(requestDTO.getAddress().getAddressLine5());
                if (requestDTO.getAddress().getAddressLine6() != null) address.setAddressLine6(requestDTO.getAddress().getAddressLine6());
                if (requestDTO.getAddress().getAddressLine7() != null) address.setAddressLine7(requestDTO.getAddress().getAddressLine7());
                soapRequest.setAddress(address);
            }
            JAXBElement<UpdateNolCustomerProfileRequest> jaxbRequest = factory.createUpdateNolCustomerProfileRequest(soapRequest);
            log.info("Prepared SOAP Request :{} ",jaxbRequest);
            UpdateNolCustomerProfileResponse soapResponse = ((JAXBElement<UpdateNolCustomerProfileResponse>) soapClientRegistry.getTemplate(SERVICE_KEY).marshalSendAndReceive(NOL_PORTAL_PROFILE, jaxbRequest)).getValue();
            log.info("Received SOAP response for updateUserProfile: {}", soapResponse);
            if (soapResponse != null && soapResponse.getResponseCode() != 0) throw new CbtIntegrationException(null, null, MessageKeyEnum.VALIDATION_FAILED.getCode(), "Update failed, response code: " + soapResponse.getResponseCode());
        } catch (Exception ex) {
            throw new CbtIntegrationException(null, null, MessageKeyEnum.SERVICE_CALL_FAILED.getCode(), "Failed to update customer profile: " + ex.getMessage());
        }
    }
}
