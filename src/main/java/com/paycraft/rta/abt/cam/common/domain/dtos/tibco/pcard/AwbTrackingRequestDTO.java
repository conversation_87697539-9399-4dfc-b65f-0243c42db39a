package com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.RequestDTO;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AwbTrackingRequestDTO extends RequestDTO {
    @NotBlank
    private String awbNumber;
    private String applicationReferenceNumber;
}
