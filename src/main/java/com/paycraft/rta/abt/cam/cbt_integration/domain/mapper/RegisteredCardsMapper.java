package com.paycraft.rta.abt.cam.cbt_integration.domain.mapper;

import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.GetRegisteredCardsResponse;
import com.paycraft.rta.abt.cam.cbt_integration.domain.dto.autogenerated.nol_card_enquiry.RegisteredCard;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.card_details.RegisteredCardListResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ConcessionTypeEnum;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class RegisteredCardsMapper {

    public static List<RegisteredCardListResponseDTO> mapToDTO(GetRegisteredCardsResponse source) {
        if (source == null) {
            return null;
        }



        List<RegisteredCardListResponseDTO> response = source.getReturn().getRegisteredCards().stream().map(
                registeredCard -> {
                    RegisteredCardListResponseDTO target = new RegisteredCardListResponseDTO();
                    // concessionExpiryDate & concessionType fields are missing in autoGeneratedDto's. So unable to map
                    target.setNolCardId(registeredCard.getTagId());
                    Optional.ofNullable(registeredCard.getConcessionExpireDate()).ifPresent(concessionExpiryDate -> target.setCardConcessionExpiryDate(concessionExpiryDate.toGregorianCalendar().toZonedDateTime().toLocalDate()));
                    Optional.ofNullable(registeredCard.getConcessionType()).ifPresent(concessionType -> {
                        Optional<ConcessionTypeEnum> concessionTypeAbt = ConcessionTypeEnum.fromCbtType(concessionType.value());
                        concessionTypeAbt.ifPresent(target::setConcessionType);
                    });
//                    target.setCardConcessionExpiryDate(registeredCard.getConcessionExpireDate().toGregorianCalendar().toZonedDateTime().toLocalDate());
//                    target.setConcessionType(ConcessionTypeEnum.valueOf(registeredCard.getConcessionType().value()));
//                    target.setNolCardId(registeredCard.getTagId());
                    return target;
                }
        ).toList();
        return response;
    }

//    public static List<RegisteredCardListResponseDTO> mapToDTOList(List<RegisteredCard> sourceList) {
//        if (sourceList == null) {
//            return Collections.emptyList();
//        }
//
//        return sourceList.stream()
//                .map(RegisteredCardsMapper::mapToDTO)
//                .collect(Collectors.toList());
//    }
}
